#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔄 向后兼容适配器
保持现有API不变，内部使用新架构组件
确保平滑迁移
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional

# 导入新架构组件
from core.pipeline import NERPipeline, PipelineConfig
from core.interfaces import ProcessingContext
from strategies.json_parsing import parse_tool_arguments_enhanced
from handlers.registry import get_function_call_registry
from handlers.ner_retrieval import NERRetrievalHandler

# 导入现有组件（保持兼容性）
from config import get_current_dataset_info
from model_interface import model_service

logger = logging.getLogger(__name__)


class EnhancedMetaCognitiveAgent:
    """
    增强版元认知智能体 - 使用新架构但保持原有API
    这是一个适配器类，内部使用新的ProcessingPipeline
    """
    
    def __init__(self, example_retriever=None):
        """初始化 - 保持与原版完全相同的接口"""
        if example_retriever is None:
            from example_retriever import example_retriever as default_retriever
            self.example_retriever = default_retriever
        else:
            self.example_retriever = example_retriever
        
        self.model_service = model_service
        self._initialization_started = False
        
        # 新架构组件
        self._pipeline = None
        self._registry_initialized = False
        
        logger.info("🔄 增强版MetaCognitiveAgent初始化 - 使用新架构")
    
    async def _ensure_new_architecture_initialized(self):
        """确保新架构组件已初始化"""
        if self._pipeline is None:
            # 创建管道配置
            config = PipelineConfig(
                max_workers=10,
                enable_caching=True,
                enable_error_recovery=True,
                default_timeout=300.0
            )
            
            # 创建NER管道
            self._pipeline = NERPipeline(
                config=config,
                model_service=self.model_service,
                example_retriever=self.example_retriever
            )
            
            # 注册FunctionCall处理器
            if not self._registry_initialized:
                registry = get_function_call_registry()
                ner_handler = NERRetrievalHandler(
                    example_retriever=self.example_retriever,
                    model_service=self.model_service
                )
                registry.register_handler(ner_handler, priority=10)
                self._registry_initialized = True
            
            logger.info("✅ 新架构组件初始化完成")
    
    async def _ensure_initialized(self):
        """保持原有方法名和行为"""
        if not self._initialization_started and self.example_retriever and not self.example_retriever.initialized:
            self._initialization_started = True
            logger.info("🔧 自动初始化示例检索器...")
            await self.example_retriever.initialize_vector_store()
        
        # 同时初始化新架构
        await self._ensure_new_architecture_initialized()
    
    def _get_current_entity_types(self) -> List[str]:
        """保持原有方法"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _get_current_label_prompt(self) -> str:
        """保持原有方法"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('label_prompt', '')
    
    def _build_stage1_prompt(self, text: str) -> str:
        """保持原有方法 - 但优化了prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""Find relevant examples to help extract entities from this text.

Entity types: {entity_types_str}
Text: "{text}"

Call retrieve_ner_examples tool with STRICT JSON format.
CRITICAL: Return ONLY valid JSON, no extra text.

Required format:
{{"description": "Brief description (20-30 words)", "k": 3}}

Examples of good descriptions:
- "Formal news text with person and organization names"
- "Technical document with location and facility mentions"
- "Social media post with casual person references"

Call the tool now with proper JSON formatting."""
    
    # === 保持原有的公共方法接口 ===
    
    def build_stage1_prompt(self, text: str) -> str:
        """公共方法：构建Stage 1的prompt"""
        return self._build_stage1_prompt(text)
    
    async def simple_retrieval(self, description: str, k: int) -> List[Dict[str, Any]]:
        """公共方法：简化的检索方法 - 使用新架构"""
        await self._ensure_initialized()
        
        if not self.example_retriever:
            logger.warning("⚠️ 示例检索器未初始化")
            return []
        
        try:
            examples = await self.example_retriever.simple_retrieve(description, k)
            return examples
        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []
    
    async def execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """公共方法：执行NER阶段 - 使用新架构的错误处理"""
        await self._ensure_initialized()
        
        # 使用新架构的NER处理（这里简化实现，使用原有逻辑但加强错误处理）
        return await self._execute_ner_stage_enhanced(text, few_shot_examples)
    
    async def extract_entities(self, text: str) -> Dict[str, List[str]]:
        """
        🚀 主要方法 - 使用新架构但保持原有API
        Stage 1: LLM分析文本并生成检索请求（使用增强JSON解析）
        Stage 2: 检索few-shot示例（使用新任务管理器）
        Stage 3: 执行NER（使用错误恢复机制）
        """
        try:
            await self._ensure_initialized()
            
            logger.info(f"🧠 开始实体提取: '{text[:50]}...'")
            
            # 创建处理上下文
            context = ProcessingContext(
                sample_id=hash(text),
                stage="entity_extraction",
                metadata={
                    "text_length": len(text),
                    "entity_types": self._get_current_entity_types()
                },
                config={"enable_enhanced_parsing": True}
            )
            
            # 如果pipeline已准备好，使用新架构
            if self._pipeline:
                result = await self._pipeline.execute(text, context)
                if result.is_success:
                    return result.data or {}
                elif result.status.value == "fallback_used":
                    return result.fallback_data or {}
            
            # 降级到原有逻辑但使用增强组件
            return await self._extract_entities_with_enhanced_components(text, context)
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return {}
    
    async def _extract_entities_with_enhanced_components(self, text: str, context: ProcessingContext) -> Dict[str, List[str]]:
        """使用增强组件的实体提取"""
        logger.info(f"🧠 Stage 1: 分析文本并生成检索请求")
        
        # Stage 1: 使用增强的JSON解析
        stage1_prompt = self._build_stage1_prompt(text)
        tools = [{"type": "function", "function": {"name": "RetrieveNERExamplesTool"}}]
        messages = [{"role": "user", "content": stage1_prompt}]
        
        response = await self.model_service.generate_with_tools_async(
            messages=messages,
            tools=tools
        )
        
        if response and hasattr(response, 'tool_calls') and response.tool_calls:
            # 使用增强的JSON解析处理工具调用
            few_shot_examples = await self._execute_retrieval_stage_enhanced(response.tool_calls, context)
            
            if few_shot_examples:
                logger.info(f"🧠 Stage 2: 基于{len(few_shot_examples)}个示例进行NER")
                return await self._execute_ner_stage_enhanced(text, few_shot_examples)
            else:
                logger.warning("❌ 未获取到few-shot示例")
                return {}
        else:
            logger.warning("❌ LLM未调用检索工具")
            return {}
    
    async def _execute_retrieval_stage_enhanced(self, tool_calls: List[Any], context: ProcessingContext) -> List[Any]:
        """增强的检索阶段 - 使用新的JSON解析策略"""
        for tool_call in tool_calls:
            if not tool_call.function or tool_call.function.name != "RetrieveNERExamplesTool":
                continue
            
            try:
                # 使用增强的JSON解析
                arguments = await parse_tool_arguments_enhanced(
                    tool_call.function.arguments,
                    context,
                    self.model_service
                )
                
                if arguments is None:
                    logger.warning("参数解析失败，使用默认参数")
                    arguments = {"description": "general NER examples", "k": 3}
                
                description = arguments.get("description", "general NER examples")
                k = arguments.get("k", 3)
                
                # 执行检索
                examples = await self.simple_retrieval(description, k)
                logger.info(f"🔍 检索完成: {description[:30]}..., 返回{len(examples)}个示例")
                return examples
                
            except Exception as e:
                logger.error(f"检索阶段失败: {e}")
                return []
        
        return []
    
    async def _execute_ner_stage_enhanced(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
        """增强的NER阶段 - 保持原有逻辑但使用错误处理"""
        from utils import robust_json_parse_ner  # 使用现有的解析函数
        
        examples_text = self._format_examples_for_context(few_shot_examples)
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{text}"

JSON output:"""

        messages = [{"role": "user", "content": ner_prompt}]

        response = await self.model_service.generate_simple_async(
            messages=messages,
            temperature=0.0
        )

        if response:
            return robust_json_parse_ner(response, entity_types)
        else:
            logger.warning("❌ Stage 2 NER失败")
            return {}
    
    def _format_examples_for_context(self, examples) -> str:
        """保持原有方法"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)
    
    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """保持原有方法"""
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)


# 替换全局实例 - 向后兼容
def get_enhanced_meta_cognitive_agent(example_retriever=None):
    """获取增强版元认知智能体实例"""
    global _enhanced_meta_cognitive_agent
    if '_enhanced_meta_cognitive_agent' not in globals():
        _enhanced_meta_cognitive_agent = EnhancedMetaCognitiveAgent(example_retriever)
    return _enhanced_meta_cognitive_agent