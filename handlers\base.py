#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧩 基础处理器接口
定义FunctionCall处理器的基础接口和抽象类
遵循AURA-X原则：统一接口设计
"""

from abc import ABC, abstractmethod
from typing import Any, List, Dict, Optional

from core.interfaces import (
    FunctionCallHandler, ProcessingResult, ProcessingStatus, 
    ProcessingContext, ToolCall
)


class BaseFunctionCallHandler(FunctionCallHandler):
    """FunctionCall处理器基类"""
    
    def __init__(self, name: str, supported_tools: List[str]):
        self.name = name
        self._supported_tools = supported_tools
        self.execution_count = 0
        self.success_count = 0
        self.failure_count = 0
    
    @property
    def supported_tools(self) -> List[str]:
        """返回支持的工具列表"""
        return self._supported_tools.copy()
    
    def can_handle(self, tool_name: str) -> bool:
        """判断是否能处理指定的工具调用"""
        return tool_name in self._supported_tools
    
    async def handle(self, tool_call: Any, context: ProcessingContext) -> ProcessingResult:
        """处理工具调用的统一入口"""
        self.execution_count += 1
        
        try:
            # 标准化工具调用格式
            standardized_call = self._standardize_tool_call(tool_call)
            
            # 验证工具调用
            if not self._validate_tool_call(standardized_call, context):
                raise ValueError(f"Invalid tool call: {standardized_call.tool_name}")
            
            # 执行具体处理逻辑
            result = await self._handle_tool_call(standardized_call, context)
            
            if result.is_success:
                self.success_count += 1
            else:
                self.failure_count += 1
            
            return result
            
        except Exception as e:
            self.failure_count += 1
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"handler": self.name, "execution_count": self.execution_count}
            )
    
    @abstractmethod
    async def _handle_tool_call(self, tool_call: ToolCall, context: ProcessingContext) -> ProcessingResult:
        """处理工具调用的具体实现 - 子类必须实现"""
        pass
    
    def _standardize_tool_call(self, tool_call: Any) -> ToolCall:
        """标准化工具调用格式"""
        if isinstance(tool_call, ToolCall):
            return tool_call
        
        # 处理OpenAI格式
        if hasattr(tool_call, 'function'):
            return ToolCall.from_openai_format(tool_call)
        
        # 处理字典格式
        if isinstance(tool_call, dict):
            return ToolCall(
                tool_name=tool_call.get('tool_name', ''),
                arguments=tool_call.get('arguments', {}),
                call_id=tool_call.get('call_id'),
                raw_data=tool_call
            )
        
        raise ValueError(f"Unsupported tool call format: {type(tool_call)}")
    
    def _validate_tool_call(self, tool_call: ToolCall, context: ProcessingContext) -> bool:
        """验证工具调用是否有效"""
        if not tool_call.tool_name:
            return False
        
        if not self.can_handle(tool_call.tool_name):
            return False
        
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        total = self.execution_count
        success_rate = (self.success_count / total) if total > 0 else 0
        
        return {
            "handler_name": self.name,
            "supported_tools": self.supported_tools,
            "execution_count": self.execution_count,
            "success_count": self.success_count,
            "failure_count": self.failure_count,
            "success_rate": success_rate
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.execution_count = 0
        self.success_count = 0
        self.failure_count = 0