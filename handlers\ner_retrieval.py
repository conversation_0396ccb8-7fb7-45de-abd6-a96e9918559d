#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 NER检索处理器
处理RetrieveNERExamplesTool的具体实现
遵循AURA-X原则：专注单一职责
"""

import logging
from typing import Any, List, Dict

from .base import BaseFunctionCallHandler
from core.interfaces import ProcessingResult, ProcessingStatus, ProcessingContext, ToolCall
from strategies.json_parsing import parse_tool_arguments_enhanced

logger = logging.getLogger(__name__)


class NERRetrievalHandler(BaseFunctionCallHandler):
    """NER检索工具处理器"""
    
    def __init__(self, example_retriever=None, model_service=None):
        super().__init__(
            name="NERRetrievalHandler",
            supported_tools=["RetrieveNERExamplesTool"]
        )
        self.example_retriever = example_retriever
        self.model_service = model_service
        
        # 默认参数
        self.default_description = "general NER examples"
        self.default_k = 3
        
        logger.info("🔧 NER检索处理器初始化完成")
    
    async def _handle_tool_call(self, tool_call: Tool<PERSON>all, context: ProcessingContext) -> ProcessingResult:
        """处理NER检索工具调用"""
        try:
            # 解析参数
            if isinstance(tool_call.arguments, str):
                # 使用增强的JSON解析
                arguments = await parse_tool_arguments_enhanced(
                    tool_call.arguments,
                    context,
                    self.model_service
                )
            else:
                arguments = tool_call.arguments
            
            if not arguments:
                logger.warning("⚠️ 参数解析失败，使用默认参数")
                arguments = {
                    "description": self.default_description,
                    "k": self.default_k
                }
            
            # 提取参数
            description = arguments.get("description", self.default_description)
            k = arguments.get("k", self.default_k)
            
            # 验证参数
            if not isinstance(description, str) or not description.strip():
                description = self.default_description
                logger.warning("⚠️ 描述参数无效，使用默认值")
            
            if not isinstance(k, int) or k <= 0:
                k = self.default_k
                logger.warning("⚠️ k参数无效，使用默认值")
            
            # 执行检索
            if not self.example_retriever:
                logger.error("❌ 示例检索器未初始化")
                return self._create_fallback_result(description, k)
            
            logger.info(f"🔍 执行NER检索: {description[:50]}..., k={k}")
            examples = await self.example_retriever.simple_retrieve(description, k)
            
            if examples:
                logger.info(f"✅ 检索成功: 返回 {len(examples)} 个示例")
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    data=examples,
                    metadata={
                        "handler": self.name,
                        "description": description,
                        "k": k,
                        "retrieved_count": len(examples)
                    }
                )
            else:
                logger.warning("⚠️ 检索结果为空，使用降级处理")
                return self._create_fallback_result(description, k)
        
        except Exception as e:
            logger.error(f"❌ NER检索处理失败: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                fallback_data=self._get_fallback_examples(),
                metadata={"handler": self.name}
            )
    
    def _create_fallback_result(self, description: str, k: int) -> ProcessingResult:
        """创建降级结果"""
        fallback_examples = self._get_fallback_examples()
        
        return ProcessingResult(
            status=ProcessingStatus.FALLBACK_USED,
            data=None,
            fallback_data=fallback_examples,
            metadata={
                "handler": self.name,
                "description": description,
                "k": k,
                "fallback_used": True
            }
        )
    
    def _get_fallback_examples(self) -> List[Dict[str, Any]]:
        """获取降级示例 - 空示例列表"""
        return []
    
    def set_example_retriever(self, example_retriever) -> None:
        """设置示例检索器"""
        self.example_retriever = example_retriever
        logger.info("🔄 示例检索器已更新")
    
    def set_model_service(self, model_service) -> None:
        """设置模型服务"""
        self.model_service = model_service
        logger.info("🔄 模型服务已更新")
    
    async def health_check(self) -> bool:
        """健康检查"""
        if not self.example_retriever:
            return False
        
        # 检查检索器是否已初始化
        if hasattr(self.example_retriever, 'initialized'):
            return self.example_retriever.initialized
        
        return True