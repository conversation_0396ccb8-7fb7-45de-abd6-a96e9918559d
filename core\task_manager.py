#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚡ 简化任务管理器
替代复杂的BatchProcessor，使用简单队列+工作池模式
遵循AURA-X原则：简化复杂逻辑，提高可靠性
"""

import asyncio
import logging
import time
from typing import Any, Callable, Awaitable, List, Optional, Dict
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor

from .interfaces import ProcessingResult, ProcessingStatus, ProcessingContext
from .error_handling import get_error_handler

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """任务定义"""
    task_id: str
    func: Callable[..., Awaitable[Any]]
    args: tuple
    kwargs: dict
    priority: int = 0
    created_at: float = None
    status: TaskStatus = TaskStatus.PENDING
    result: Any = None
    error: Optional[Exception] = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = time.time()


class SimpleTaskManager:
    """简化的任务管理器 - 替代复杂的BatchProcessor"""
    
    def __init__(self, max_workers: int = 10, queue_size: int = 1000):
        self.max_workers = max_workers
        self.queue_size = queue_size
        self.task_queue = asyncio.Queue(maxsize=queue_size)
        self.active_tasks: Dict[str, Task] = {}
        self.completed_tasks: Dict[str, Task] = {}
        self.workers: List[asyncio.Task] = []
        self.running = False
        self._shutdown_event = asyncio.Event()
        self.error_handler = get_error_handler()
        
        # 统计信息
        self.stats = {
            "total_submitted": 0,
            "total_completed": 0,
            "total_failed": 0,
            "total_cancelled": 0
        }
    
    async def start(self):
        """启动任务管理器"""
        if self.running:
            return
        
        self.running = True
        self._shutdown_event.clear()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self.workers.append(worker)
        
        logger.info(f"✅ 任务管理器已启动: {self.max_workers} 个工作线程")
    
    async def stop(self, timeout: float = 30.0):
        """停止任务管理器"""
        if not self.running:
            return
        
        logger.info("🛑 开始停止任务管理器...")
        self.running = False
        self._shutdown_event.set()
        
        # 取消所有待处理任务
        while not self.task_queue.empty():
            try:
                task = self.task_queue.get_nowait()
                task.status = TaskStatus.CANCELLED
                self.stats["total_cancelled"] += 1
            except asyncio.QueueEmpty:
                break
        
        # 等待工作线程完成
        if self.workers:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.workers, return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning("⚠️ 工作线程停止超时，强制取消")
                for worker in self.workers:
                    if not worker.done():
                        worker.cancel()
        
        self.workers.clear()
        logger.info("✅ 任务管理器已停止")
    
    async def submit_task(
        self,
        task_id: str,
        func: Callable[..., Awaitable[Any]],
        *args,
        priority: int = 0,
        **kwargs
    ) -> Task:
        """提交任务"""
        if not self.running:
            await self.start()
        
        task = Task(
            task_id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority
        )
        
        try:
            await self.task_queue.put(task)
            self.stats["total_submitted"] += 1
            logger.debug(f"📝 任务已提交: {task_id}")
            return task
        except asyncio.QueueFull:
            raise Exception(f"任务队列已满: {self.queue_size}")
    
    async def submit_batch(
        self,
        tasks: List[tuple],  # [(task_id, func, args, kwargs), ...]
        priority: int = 0
    ) -> List[Task]:
        """批量提交任务"""
        submitted_tasks = []
        
        for task_info in tasks:
            if len(task_info) >= 3:
                task_id, func, args = task_info[:3]
                kwargs = task_info[3] if len(task_info) > 3 else {}
                
                task = await self.submit_task(task_id, func, *args, priority=priority, **kwargs)
                submitted_tasks.append(task)
        
        logger.info(f"📦 批量提交完成: {len(submitted_tasks)} 个任务")
        return submitted_tasks
    
    async def wait_for_task(self, task_id: str, timeout: float = None) -> Task:
        """等待特定任务完成"""
        start_time = time.time()
        
        while True:
            # 检查已完成任务
            if task_id in self.completed_tasks:
                return self.completed_tasks[task_id]
            
            # 检查活动任务
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    return task
            
            # 检查超时
            if timeout and (time.time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"等待任务超时: {task_id}")
            
            await asyncio.sleep(0.1)
    
    async def wait_for_all(self, task_ids: List[str], timeout: float = None) -> List[Task]:
        """等待所有指定任务完成"""
        tasks = []
        
        # 并发等待所有任务
        wait_tasks = [
            self.wait_for_task(task_id, timeout)
            for task_id in task_ids
        ]
        
        try:
            results = await asyncio.gather(*wait_tasks, return_exceptions=True)
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"❌ 等待任务失败: {result}")
                else:
                    tasks.append(result)
        except Exception as e:
            logger.error(f"❌ 等待所有任务失败: {e}")
        
        return tasks
    
    async def _worker(self, worker_name: str):
        """工作线程主循环"""
        logger.debug(f"🚀 工作线程启动: {worker_name}")
        
        while self.running and not self._shutdown_event.is_set():
            try:
                # 从队列中获取任务（带超时）
                task = await asyncio.wait_for(
                    self.task_queue.get(),
                    timeout=1.0
                )
                
                # 执行任务
                await self._execute_task(task, worker_name)
                
            except asyncio.TimeoutError:
                # 队列为空，继续循环
                continue
            except Exception as e:
                logger.error(f"❌ 工作线程异常 {worker_name}: {e}")
                await asyncio.sleep(1.0)
        
        logger.debug(f"🛑 工作线程停止: {worker_name}")
    
    async def _execute_task(self, task: Task, worker_name: str):
        """执行单个任务"""
        task.status = TaskStatus.RUNNING
        self.active_tasks[task.task_id] = task
        
        # 创建处理上下文
        context = ProcessingContext(
            sample_id=hash(task.task_id),
            stage="task_execution",
            metadata={"worker": worker_name, "task_id": task.task_id},
            config={}
        )
        
        try:
            logger.debug(f"▶️ 开始执行任务: {task.task_id} (worker: {worker_name})")
            
            # 使用错误处理器执行任务
            result = await self.error_handler.execute_with_protection(
                task.func,
                context,
                circuit_breaker_name=f"task_execution",
                *task.args,
                **task.kwargs
            )
            
            # 处理结果
            if result.is_success:
                task.status = TaskStatus.COMPLETED
                task.result = result.data
                self.stats["total_completed"] += 1
                logger.debug(f"✅ 任务完成: {task.task_id}")
            else:
                task.status = TaskStatus.FAILED
                task.error = result.error
                task.result = result.fallback_data  # 使用降级数据
                self.stats["total_failed"] += 1
                logger.warning(f"❌ 任务失败: {task.task_id}, 错误: {result.error}")
        
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = e
            self.stats["total_failed"] += 1
            logger.error(f"❌ 任务执行异常: {task.task_id}, 错误: {e}")
        
        finally:
            # 移动到已完成任务
            if task.task_id in self.active_tasks:
                del self.active_tasks[task.task_id]
            self.completed_tasks[task.task_id] = task
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "queue_size": self.task_queue.qsize(),
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len(self.completed_tasks),
            "workers": len(self.workers),
            "running": self.running
        }
    
    def clear_completed_tasks(self, older_than: float = None):
        """清理已完成的任务"""
        if older_than is None:
            older_than = time.time() - 3600  # 默认清理1小时前的任务
        
        to_remove = []
        for task_id, task in self.completed_tasks.items():
            if task.created_at < older_than:
                to_remove.append(task_id)
        
        for task_id in to_remove:
            del self.completed_tasks[task_id]
        
        if to_remove:
            logger.info(f"🧹 清理了 {len(to_remove)} 个已完成任务")


# 全局任务管理器实例
_global_task_manager = None

async def get_task_manager(max_workers: int = 10) -> SimpleTaskManager:
    """获取全局任务管理器"""
    global _global_task_manager
    if _global_task_manager is None:
        _global_task_manager = SimpleTaskManager(max_workers=max_workers)
        await _global_task_manager.start()
    return _global_task_manager


async def shutdown_task_manager():
    """关闭全局任务管理器"""
    global _global_task_manager
    if _global_task_manager:
        await _global_task_manager.stop()
        _global_task_manager = None


# 便利函数
async def run_async_batch(
    tasks: List[tuple],
    max_workers: int = 10,
    timeout: float = None
) -> List[Task]:
    """运行异步批处理任务的便利函数"""
    task_manager = await get_task_manager(max_workers)
    
    # 提交所有任务
    submitted_tasks = await task_manager.submit_batch(tasks)
    task_ids = [task.task_id for task in submitted_tasks]
    
    # 等待所有任务完成
    completed_tasks = await task_manager.wait_for_all(task_ids, timeout)
    
    return completed_tasks