#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🛡️ 统一错误处理机制
基于Circuit Breaker模式和智能降级策略
遵循AURA-X原则：自动化错误恢复和智能降级
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Callable, Awaitable
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps

from .interfaces import ProcessingResult, ProcessingStatus, ProcessingContext, RetryStrategy

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"         # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态（尝试恢复）


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5       # 失败阈值
    recovery_timeout: float = 60.0   # 恢复超时时间（秒）
    success_threshold: int = 3       # 半开状态下的成功阈值
    timeout: float = 30.0           # 单次调用超时时间


class CircuitBreaker:
    """熔断器实现 - 防止级联失败"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self._lock = asyncio.Lock()
    
    async def call(self, func: Callable[..., Awaitable[Any]], *args, **kwargs) -> Any:
        """执行受保护的异步调用"""
        async with self._lock:
            if self.state == CircuitState.OPEN:
                if time.time() - self.last_failure_time > self.config.recovery_timeout:
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info(f"🔄 熔断器 {self.name} 进入半开状态")
                else:
                    raise CircuitBreakerOpenError(f"熔断器 {self.name} 处于开启状态")
        
        try:
            # 设置超时
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=self.config.timeout
            )
            
            # 成功处理
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure()
            raise
    
    async def _on_success(self):
        """处理成功回调"""
        async with self._lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    logger.info(f"✅ 熔断器 {self.name} 恢复正常状态")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    async def _on_failure(self):
        """处理失败回调"""
        async with self._lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.CLOSED:
                if self.failure_count >= self.config.failure_threshold:
                    self.state = CircuitState.OPEN
                    logger.warning(f"🚨 熔断器 {self.name} 开启保护")
            elif self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning(f"🚨 熔断器 {self.name} 重新开启保护")


class CircuitBreakerOpenError(Exception):
    """熔断器开启异常"""
    pass


@dataclass
class ErrorContext:
    """错误上下文信息"""
    error: Exception
    function_name: str
    args: tuple
    kwargs: Dict[str, Any]
    retry_count: int = 0
    timestamp: float = field(default_factory=time.time)


class DefaultRetryStrategy:
    """默认重试策略 - 指数退避"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def should_retry(self, error: Exception, context: ProcessingContext) -> bool:
        """判断是否应该重试"""
        if context.retry_count >= self.max_retries:
            return False
        
        # 某些错误不重试
        if isinstance(error, (KeyboardInterrupt, SystemExit, CircuitBreakerOpenError)):
            return False
        
        return True
    
    def get_retry_delay(self, retry_count: int) -> float:
        """获取重试延迟时间（指数退避）"""
        delay = self.base_delay * (2 ** retry_count)
        return min(delay, self.max_delay)
    
    def get_max_retries(self) -> int:
        return self.max_retries


class UnifiedErrorHandler:
    """统一错误处理器 - 系统的错误处理中心"""
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_strategy = DefaultRetryStrategy()
        self.fallback_handlers: Dict[str, Callable] = {}
        self.error_statistics: Dict[str, int] = {}
    
    def register_circuit_breaker(self, name: str, config: CircuitBreakerConfig = None) -> CircuitBreaker:
        """注册熔断器"""
        if config is None:
            config = CircuitBreakerConfig()
        
        circuit_breaker = CircuitBreaker(name, config)
        self.circuit_breakers[name] = circuit_breaker
        return circuit_breaker
    
    def register_fallback(self, error_type: str, handler: Callable) -> None:
        """注册降级处理器"""
        self.fallback_handlers[error_type] = handler
    
    async def execute_with_protection(
        self,
        func: Callable[..., Awaitable[Any]],
        context: ProcessingContext,
        circuit_breaker_name: Optional[str] = None,
        *args,
        **kwargs
    ) -> ProcessingResult:
        """执行受保护的异步调用"""
        
        # 选择熔断器
        circuit_breaker = None
        if circuit_breaker_name:
            circuit_breaker = self.circuit_breakers.get(circuit_breaker_name)
            if not circuit_breaker:
                # 自动创建熔断器
                circuit_breaker = self.register_circuit_breaker(circuit_breaker_name)
        
        while context.retry_count <= self.retry_strategy.get_max_retries():
            try:
                # 使用熔断器保护
                if circuit_breaker:
                    result = await circuit_breaker.call(func, *args, **kwargs)
                else:
                    result = await func(*args, **kwargs)
                
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    data=result,
                    metadata={"retry_count": context.retry_count}
                )
                
            except Exception as e:
                # 记录错误
                context.error_history.append(e)
                error_type = type(e).__name__
                self.error_statistics[error_type] = self.error_statistics.get(error_type, 0) + 1
                
                logger.warning(f"⚠️ 执行失败 [{context.stage}]: {e} (重试: {context.retry_count})")
                
                # 判断是否重试
                if self.retry_strategy.should_retry(e, context):
                    context.retry_count += 1
                    delay = self.retry_strategy.get_retry_delay(context.retry_count)
                    await asyncio.sleep(delay)
                    continue
                
                # 尝试降级处理
                fallback_result = await self._try_fallback(error_type, e, context)
                if fallback_result:
                    return ProcessingResult(
                        status=ProcessingStatus.FALLBACK_USED,
                        data=None,
                        fallback_data=fallback_result,
                        error=e,
                        metadata={"retry_count": context.retry_count}
                    )
                
                # 彻底失败
                return ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=e,
                    metadata={"retry_count": context.retry_count}
                )
        
        # 超过最大重试次数
        last_error = context.error_history[-1] if context.error_history else Exception("Unknown error")
        return ProcessingResult(
            status=ProcessingStatus.FAILED,
            data=None,
            error=last_error,
            metadata={"retry_count": context.retry_count}
        )
    
    async def _try_fallback(self, error_type: str, error: Exception, context: ProcessingContext) -> Any:
        """尝试降级处理"""
        fallback_handler = self.fallback_handlers.get(error_type)
        if fallback_handler:
            try:
                logger.info(f"🔄 使用降级处理器: {error_type}")
                return await fallback_handler(error, context)
            except Exception as fallback_error:
                logger.error(f"❌ 降级处理失败: {fallback_error}")
        
        return None
    
    def get_error_statistics(self) -> Dict[str, int]:
        """获取错误统计信息"""
        return self.error_statistics.copy()
    
    def reset_error_statistics(self) -> None:
        """重置错误统计"""
        self.error_statistics.clear()


def with_error_protection(
    circuit_breaker_name: Optional[str] = None,
    error_handler: Optional[UnifiedErrorHandler] = None
):
    """错误保护装饰器 - 简化错误处理的使用"""
    
    def decorator(func: Callable[..., Awaitable[Any]]):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 获取或创建错误处理器
            handler = error_handler or _get_global_error_handler()
            
            # 创建处理上下文
            context = ProcessingContext(
                sample_id=kwargs.get('sample_id', 0),
                stage=func.__name__,
                metadata={},
                config={}
            )
            
            result = await handler.execute_with_protection(
                func, context, circuit_breaker_name, *args, **kwargs
            )
            
            if result.is_success:
                return result.data
            elif result.status == ProcessingStatus.FALLBACK_USED:
                return result.fallback_data
            else:
                raise result.error
        
        return wrapper
    return decorator


# 全局错误处理器实例
_global_error_handler = None

def _get_global_error_handler() -> UnifiedErrorHandler:
    """获取全局错误处理器"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = UnifiedErrorHandler()
    return _global_error_handler


def get_error_handler() -> UnifiedErrorHandler:
    """获取全局错误处理器实例"""
    return _get_global_error_handler()