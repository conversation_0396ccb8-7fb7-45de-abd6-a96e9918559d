#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏗️ 核心接口定义
定义系统中所有组件的标准接口，支持插件化扩展
遵循AURA-X原则：统一、可适应的接口设计
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Protocol, Union
from dataclasses import dataclass
from enum import Enum


class ProcessingStatus(Enum):
    """处理状态枚举"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"
    RETRY_NEEDED = "retry_needed"
    FALLBACK_USED = "fallback_used"


@dataclass
class ProcessingContext:
    """处理上下文 - 在整个处理流程中传递的状态信息"""
    sample_id: int
    stage: str
    metadata: Dict[str, Any]
    config: Dict[str, Any]
    retry_count: int = 0
    error_history: List[Exception] = None
    
    def __post_init__(self):
        if self.error_history is None:
            self.error_history = []


@dataclass
class ProcessingResult:
    """标准化的处理结果"""
    status: ProcessingStatus
    data: Any
    error: Optional[Exception] = None
    fallback_data: Optional[Any] = None
    metadata: Dict[str, Any] = None
    retry_suggested: bool = False
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    @property
    def is_success(self) -> bool:
        return self.status == ProcessingStatus.SUCCESS
    
    @property
    def needs_retry(self) -> bool:
        return self.status == ProcessingStatus.RETRY_NEEDED or self.retry_suggested


class ProcessingStrategy(Protocol):
    """处理策略接口 - 所有策略都应实现此接口"""
    
    async def process(self, input_data: Any, context: ProcessingContext) -> ProcessingResult:
        """处理输入数据并返回结果"""
        ...
    
    def can_handle(self, input_data: Any, context: ProcessingContext) -> bool:
        """判断是否能处理给定的输入"""
        ...


class FunctionCallHandler(Protocol):
    """Function Call处理器接口"""
    
    def can_handle(self, tool_name: str) -> bool:
        """判断是否能处理指定的工具调用"""
        ...
    
    async def handle(self, tool_call: Any, context: ProcessingContext) -> ProcessingResult:
        """处理工具调用"""
        ...
    
    @property
    def supported_tools(self) -> List[str]:
        """返回支持的工具列表"""
        ...


class WeightingStrategy(Protocol):
    """加权策略接口"""
    
    def calculate_weights(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """计算候选项的权重"""
        ...
    
    def get_config(self) -> Dict[str, float]:
        """获取当前权重配置"""
        ...
    
    def update_config(self, config: Dict[str, float]) -> None:
        """更新权重配置"""
        ...


class RetryStrategy(Protocol):
    """重试策略接口"""
    
    def should_retry(self, error: Exception, context: ProcessingContext) -> bool:
        """判断是否应该重试"""
        ...
    
    def get_retry_delay(self, retry_count: int) -> float:
        """获取重试延迟时间"""
        ...
    
    def get_max_retries(self) -> int:
        """获取最大重试次数"""
        ...


class BaseProcessor(ABC):
    """基础处理器抽象类"""
    
    def __init__(self, name: str):
        self.name = name
        self._strategies: List[ProcessingStrategy] = []
        self._error_handlers: List[Any] = []
    
    def add_strategy(self, strategy: ProcessingStrategy) -> None:
        """添加处理策略"""
        self._strategies.append(strategy)
    
    def remove_strategy(self, strategy: ProcessingStrategy) -> None:
        """移除处理策略"""
        if strategy in self._strategies:
            self._strategies.remove(strategy)
    
    @abstractmethod
    async def process(self, input_data: Any, context: ProcessingContext) -> ProcessingResult:
        """处理数据的抽象方法"""
        pass


class ConfigurableComponent(Protocol):
    """可配置组件接口"""
    
    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        ...
    
    def update_config(self, config: Dict[str, Any]) -> None:
        """更新配置"""
        ...
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证配置有效性"""
        ...


@dataclass
class ToolCall:
    """标准化的工具调用结构"""
    tool_name: str
    arguments: Dict[str, Any]
    call_id: Optional[str] = None
    raw_data: Optional[Any] = None  # 原始调用数据
    
    @classmethod
    def from_openai_format(cls, openai_tool_call: Any) -> 'ToolCall':
        """从OpenAI格式转换"""
        return cls(
            tool_name=openai_tool_call.function.name,
            arguments=openai_tool_call.function.arguments,
            call_id=getattr(openai_tool_call, 'id', None),
            raw_data=openai_tool_call
        )


class Pipeline(Protocol):
    """处理管道接口"""
    
    async def execute(self, input_data: Any, context: ProcessingContext) -> ProcessingResult:
        """执行处理管道"""
        ...
    
    def add_stage(self, stage_name: str, processor: BaseProcessor) -> None:
        """添加处理阶段"""
        ...
    
    def remove_stage(self, stage_name: str) -> None:
        """移除处理阶段"""
        ...
    
    def get_stages(self) -> List[str]:
        """获取所有阶段名称"""
        ...


# 常用类型别名，提高代码可读性
JSONData = Union[Dict[str, Any], List[Any], str, int, float, bool, None]
EntityLabels = Dict[str, List[str]]
ExampleData = Dict[str, Any]
EmbeddingVector = List[float]