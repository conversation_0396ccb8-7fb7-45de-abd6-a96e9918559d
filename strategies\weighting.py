#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
⚖️ 加权策略实现
支持多种可配置的加权算法，用于检索结果排序和选择
遵循AURA-X原则：可适应的权重配置
"""

import logging
import math
from typing import Any, Dict, List, Optional
from abc import ABC, abstractmethod
from dataclasses import dataclass, field

from core.interfaces import WeightingStrategy, ProcessingContext

logger = logging.getLogger(__name__)


@dataclass
class WeightingConfig:
    """加权配置"""
    similarity_weight: float = 0.4      # 相似度权重
    diversity_weight: float = 0.3       # 多样性权重
    recency_weight: float = 0.2         # 新近度权重
    domain_relevance_weight: float = 0.1 # 领域相关性权重
    
    # 高级配置
    enable_normalization: bool = True    # 启用权重归一化
    min_similarity_threshold: float = 0.1  # 最小相似度阈值
    diversity_penalty_factor: float = 0.8   # 多样性惩罚因子
    
    def __post_init__(self):
        """后处理：验证和归一化权重"""
        if self.enable_normalization:
            total = (self.similarity_weight + self.diversity_weight + 
                    self.recency_weight + self.domain_relevance_weight)
            if total > 0:
                self.similarity_weight /= total
                self.diversity_weight /= total
                self.recency_weight /= total
                self.domain_relevance_weight /= total
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典格式"""
        return {
            "similarity_weight": self.similarity_weight,
            "diversity_weight": self.diversity_weight,
            "recency_weight": self.recency_weight,
            "domain_relevance_weight": self.domain_relevance_weight,
            "enable_normalization": self.enable_normalization,
            "min_similarity_threshold": self.min_similarity_threshold,
            "diversity_penalty_factor": self.diversity_penalty_factor
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'WeightingConfig':
        """从字典创建配置"""
        return cls(**{k: v for k, v in config_dict.items() if hasattr(cls, k)})


class BaseWeightingStrategy(WeightingStrategy):
    """基础加权策略抽象类"""
    
    def __init__(self, name: str, config: WeightingConfig = None):
        self.name = name
        self.config = config or WeightingConfig()
        self.calculation_count = 0
        
    def get_config(self) -> Dict[str, float]:
        """获取当前权重配置"""
        return self.config.to_dict()
    
    def update_config(self, config: Dict[str, float]) -> None:
        """更新权重配置"""
        # 保留原配置，只更新提供的值
        current_dict = self.config.to_dict()
        current_dict.update(config)
        self.config = WeightingConfig.from_dict(current_dict)
        logger.info(f"🔄 权重策略 {self.name} 配置已更新")
    
    @abstractmethod
    def calculate_weights(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """计算候选项权重的抽象方法"""
        pass
    
    def _extract_similarity_scores(self, candidates: List[Any]) -> List[float]:
        """提取相似度分数"""
        scores = []
        for candidate in candidates:
            if isinstance(candidate, dict):
                score = candidate.get('similarity_score', candidate.get('score', 0.5))
            elif hasattr(candidate, 'similarity_score'):
                score = candidate.similarity_score
            elif hasattr(candidate, 'score'):
                score = candidate.score
            else:
                score = 0.5  # 默认分数
            scores.append(float(score))
        return scores
    
    def _calculate_diversity_scores(self, candidates: List[Any]) -> List[float]:
        """计算多样性分数"""
        if len(candidates) <= 1:
            return [1.0] * len(candidates)
        
        diversity_scores = []
        
        for i, candidate in enumerate(candidates):
            # 简单的多样性计算：基于文本长度和内容差异
            diversity_score = 1.0
            
            # 获取候选项的文本内容
            text = self._extract_text_content(candidate)
            if not text:
                diversity_scores.append(0.5)
                continue
            
            # 与其他候选项比较
            for j, other_candidate in enumerate(candidates):
                if i == j:
                    continue
                
                other_text = self._extract_text_content(other_candidate)
                if not other_text:
                    continue
                
                # 简单的文本相似度（基于长度和重叠词汇）
                similarity = self._text_similarity(text, other_text)
                diversity_score *= (1.0 - similarity * self.config.diversity_penalty_factor)
            
            diversity_scores.append(max(0.1, diversity_score))  # 确保最小值
        
        return diversity_scores
    
    def _extract_text_content(self, candidate: Any) -> str:
        """提取候选项的文本内容"""
        if isinstance(candidate, dict):
            return candidate.get('text', candidate.get('content', ''))
        elif hasattr(candidate, 'text'):
            return candidate.text
        elif hasattr(candidate, 'content'):
            return candidate.content
        elif isinstance(candidate, str):
            return candidate
        else:
            return ''
    
    def _text_similarity(self, text1: str, text2: str) -> float:
        """计算文本相似度（简单实现）"""
        if not text1 or not text2:
            return 0.0
        
        # 简单的词汇重叠相似度
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_recency_scores(self, candidates: List[Any]) -> List[float]:
        """计算新近度分数"""
        # 简单实现：基于候选项在列表中的位置
        # 假设候选项按相关性排序，早期的更相关
        if not candidates:
            return []
        
        scores = []
        for i, candidate in enumerate(candidates):
            # 新近度分数随位置递减
            recency_score = math.exp(-i * 0.1)  # 指数衰减
            scores.append(recency_score)
        
        return scores
    
    def _calculate_domain_relevance_scores(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """计算领域相关性分数"""
        # 基于上下文信息计算领域相关性
        domain_keywords = context.metadata.get('domain_keywords', [])
        
        if not domain_keywords:
            return [0.5] * len(candidates)  # 默认中等相关性
        
        scores = []
        for candidate in candidates:
            text = self._extract_text_content(candidate)
            if not text:
                scores.append(0.5)
                continue
            
            # 计算领域关键词匹配度
            text_words = set(text.lower().split())
            keyword_matches = sum(1 for keyword in domain_keywords if keyword.lower() in text_words)
            
            relevance_score = min(1.0, keyword_matches / len(domain_keywords))
            scores.append(relevance_score)
        
        return scores


class StandardWeightingStrategy(BaseWeightingStrategy):
    """标准加权策略 - 综合考虑多个因素"""
    
    def __init__(self, config: WeightingConfig = None):
        super().__init__("StandardWeightingStrategy", config)
    
    def calculate_weights(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """计算综合权重"""
        self.calculation_count += 1
        
        if not candidates:
            return []
        
        # 提取各维度分数
        similarity_scores = self._extract_similarity_scores(candidates)
        diversity_scores = self._calculate_diversity_scores(candidates)
        recency_scores = self._calculate_recency_scores(candidates)
        domain_scores = self._calculate_domain_relevance_scores(candidates, context)
        
        # 确保所有分数列表长度一致
        n = len(candidates)
        similarity_scores = similarity_scores[:n] + [0.5] * max(0, n - len(similarity_scores))
        diversity_scores = diversity_scores[:n] + [0.5] * max(0, n - len(diversity_scores))
        recency_scores = recency_scores[:n] + [0.5] * max(0, n - len(recency_scores))
        domain_scores = domain_scores[:n] + [0.5] * max(0, n - len(domain_scores))
        
        # 计算综合权重
        final_weights = []
        for i in range(n):
            weight = (
                self.config.similarity_weight * similarity_scores[i] +
                self.config.diversity_weight * diversity_scores[i] +
                self.config.recency_weight * recency_scores[i] +
                self.config.domain_relevance_weight * domain_scores[i]
            )
            
            # 应用最小阈值
            if similarity_scores[i] < self.config.min_similarity_threshold:
                weight *= 0.1  # 大幅降低权重
            
            final_weights.append(max(0.0, weight))
        
        # 归一化权重
        if self.config.enable_normalization and final_weights:
            total_weight = sum(final_weights)
            if total_weight > 0:
                final_weights = [w / total_weight for w in final_weights]
        
        logger.debug(f"🔢 权重计算完成: {len(final_weights)} 个权重, 总和: {sum(final_weights):.3f}")
        return final_weights


class SimilarityOnlyStrategy(BaseWeightingStrategy):
    """仅基于相似度的简单策略"""
    
    def __init__(self, config: WeightingConfig = None):
        if config is None:
            config = WeightingConfig(
                similarity_weight=1.0,
                diversity_weight=0.0,
                recency_weight=0.0,
                domain_relevance_weight=0.0
            )
        super().__init__("SimilarityOnlyStrategy", config)
    
    def calculate_weights(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """仅基于相似度计算权重"""
        self.calculation_count += 1
        
        similarity_scores = self._extract_similarity_scores(candidates)
        
        # 应用最小阈值
        weights = []
        for score in similarity_scores:
            if score < self.config.min_similarity_threshold:
                weights.append(0.0)
            else:
                weights.append(score)
        
        # 归一化
        if self.config.enable_normalization and weights:
            total = sum(weights)
            if total > 0:
                weights = [w / total for w in weights]
        
        return weights


class AdaptiveWeightingStrategy(BaseWeightingStrategy):
    """自适应加权策略 - 根据历史性能调整权重"""
    
    def __init__(self, config: WeightingConfig = None):
        super().__init__("AdaptiveWeightingStrategy", config)
        self.performance_history = []
        self.adaptation_rate = 0.1
    
    def calculate_weights(self, candidates: List[Any], context: ProcessingContext) -> List[float]:
        """自适应权重计算"""
        self.calculation_count += 1
        
        # 基础权重计算（同标准策略）
        base_strategy = StandardWeightingStrategy(self.config)
        weights = base_strategy.calculate_weights(candidates, context)
        
        # 根据历史性能调整权重
        if self.performance_history:
            avg_performance = sum(self.performance_history) / len(self.performance_history)
            
            # 如果性能不佳，增加多样性权重
            if avg_performance < 0.6:
                self.config.diversity_weight += self.adaptation_rate
                self.config.similarity_weight -= self.adaptation_rate * 0.5
            # 如果性能良好，增加相似度权重
            elif avg_performance > 0.8:
                self.config.similarity_weight += self.adaptation_rate
                self.config.diversity_weight -= self.adaptation_rate * 0.5
            
            # 重新归一化
            if self.config.enable_normalization:
                total = (self.config.similarity_weight + self.config.diversity_weight + 
                        self.config.recency_weight + self.config.domain_relevance_weight)
                if total > 0:
                    self.config.similarity_weight /= total
                    self.config.diversity_weight /= total
                    self.config.recency_weight /= total
                    self.config.domain_relevance_weight /= total
        
        return weights
    
    def update_performance(self, performance_score: float) -> None:
        """更新性能记录"""
        self.performance_history.append(performance_score)
        
        # 保留最近100次记录
        if len(self.performance_history) > 100:
            self.performance_history = self.performance_history[-100:]


# 工厂函数
def create_weighting_strategy(strategy_type: str, config: WeightingConfig = None) -> BaseWeightingStrategy:
    """创建加权策略"""
    strategies = {
        "standard": StandardWeightingStrategy,
        "similarity_only": SimilarityOnlyStrategy,
        "adaptive": AdaptiveWeightingStrategy
    }
    
    if strategy_type not in strategies:
        raise ValueError(f"未知的加权策略类型: {strategy_type}")
    
    return strategies[strategy_type](config)


# 便利函数
def apply_weights_to_candidates(
    candidates: List[Any], 
    weights: List[float], 
    top_k: int = None
) -> List[Any]:
    """应用权重到候选项并排序"""
    if len(candidates) != len(weights):
        raise ValueError("候选项和权重数量不匹配")
    
    # 组合候选项和权重
    weighted_candidates = list(zip(candidates, weights))
    
    # 按权重排序（降序）
    weighted_candidates.sort(key=lambda x: x[1], reverse=True)
    
    # 提取排序后的候选项
    sorted_candidates = [candidate for candidate, weight in weighted_candidates]
    
    # 返回top-k结果
    if top_k:
        return sorted_candidates[:top_k]
    
    return sorted_candidates