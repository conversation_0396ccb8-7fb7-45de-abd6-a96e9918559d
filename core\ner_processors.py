#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🎯 NER专用处理器
实现三阶段NER处理流程的具体处理器
遵循AURA-X原则：模块化、可复用的处理器组件
"""

import logging
from typing import Any, Dict, List, Optional

from .interfaces import BaseProcessor, ProcessingResult, ProcessingStatus, ProcessingContext
from .error_handling import get_error_handler
from strategies.json_parsing import MultiLevelJSONParser, parse_tool_arguments_enhanced
from schemas import RetrieveNERExamplesTool
from config import get_current_dataset_info
from utils import robust_json_parse_ner

logger = logging.getLogger(__name__)


class Stage1AnalysisProcessor(BaseProcessor):
    """阶段1：文本分析和检索请求生成处理器"""
    
    def __init__(self, model_service=None):
        super().__init__("Stage1AnalysisProcessor")
        self.model_service = model_service
        self.json_parser = MultiLevelJSONParser(model_service)
    
    async def process(self, input_data: str, context: ProcessingContext) -> ProcessingResult:
        """处理文本，生成检索请求"""
        try:
            text = input_data
            logger.info(f"🧠 Stage 1: 分析文本并生成检索请求")
            
            # 构建prompt
            stage1_prompt = self._build_stage1_prompt(text)
            
            # 调用LLM
            tools = [{"type": "function", "function": {"name": "RetrieveNERExamplesTool"}}]
            messages = [{"role": "user", "content": stage1_prompt}]
            
            response = await self.model_service.generate_with_tools_async(
                messages=messages,
                tools=tools
            )
            
            if response and hasattr(response, 'tool_calls') and response.tool_calls:
                # 解析工具调用参数
                for tool_call in response.tool_calls:
                    if tool_call.function.name == "RetrieveNERExamplesTool":
                        # 使用增强的JSON解析
                        arguments = await parse_tool_arguments_enhanced(
                            tool_call.function.arguments,
                            context,
                            self.model_service
                        )
                        
                        if arguments is None:
                            logger.warning("参数解析失败，使用默认参数")
                            arguments = {"description": "general NER examples", "k": 3}
                        
                        return ProcessingResult(
                            status=ProcessingStatus.SUCCESS,
                            data={
                                "description": arguments.get("description", "general NER examples"),
                                "k": arguments.get("k", 3),
                                "original_text": text
                            },
                            metadata={
                                "stage": "analysis",
                                "parser_used": "enhanced_json"
                            }
                        )
            
            # 没有工具调用，使用默认值
            logger.warning("❌ LLM未调用检索工具，使用默认参数")
            return ProcessingResult(
                status=ProcessingStatus.FALLBACK_USED,
                data=None,
                fallback_data={
                    "description": "general NER examples",
                    "k": 3,
                    "original_text": text
                },
                metadata={"stage": "analysis", "fallback_reason": "no_tool_call"}
            )
            
        except Exception as e:
            logger.error(f"❌ Stage 1处理失败: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"stage": "analysis"}
            )
    
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _build_stage1_prompt(self, text: str) -> str:
        """构建Stage 1的prompt"""
        entity_types = self._get_current_entity_types()
        entity_types_str = ', '.join(entity_types)

        return f"""Find relevant examples to help extract entities from this text.

Entity types: {entity_types_str}
Text: "{text}"

Call retrieve_ner_examples tool with STRICT JSON format.
CRITICAL: Return ONLY valid JSON, no extra text.

Required format:
{{"description": "Brief description (20-30 words)", "k": 3}}

Examples of good descriptions:
- "Formal news text with person and organization names"
- "Technical document with location and facility mentions"
- "Social media post with casual person references"

Call the tool now with proper JSON formatting."""


class Stage2RetrievalProcessor(BaseProcessor):
    """阶段2：示例检索处理器"""
    
    def __init__(self, example_retriever=None):
        super().__init__("Stage2RetrievalProcessor")
        self.example_retriever = example_retriever
    
    async def process(self, input_data: Dict[str, Any], context: ProcessingContext) -> ProcessingResult:
        """根据描述检索相关示例"""
        try:
            description = input_data.get("description", "general NER examples")
            k = input_data.get("k", 3)
            original_text = input_data.get("original_text", "")
            
            logger.info(f"🔍 Stage 2: 检索示例 - {description[:30]}...")
            
            if not self.example_retriever:
                logger.warning("⚠️ 示例检索器未初始化")
                return ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=Exception("Example retriever not available"),
                    metadata={"stage": "retrieval"}
                )
            
            # 执行检索
            examples = await self.example_retriever.simple_retrieve(description, k)
            
            logger.info(f"✅ 检索完成: 返回 {len(examples)} 个示例")
            
            return ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                data={
                    "examples": examples,
                    "original_text": original_text,
                    "retrieval_description": description,
                    "requested_k": k
                },
                metadata={
                    "stage": "retrieval",
                    "examples_found": len(examples)
                }
            )
            
        except Exception as e:
            logger.error(f"❌ Stage 2检索失败: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"stage": "retrieval"}
            )


class Stage3NERProcessor(BaseProcessor):
    """阶段3：NER执行处理器"""
    
    def __init__(self, model_service=None):
        super().__init__("Stage3NERProcessor")
        self.model_service = model_service
    
    async def process(self, input_data: Dict[str, Any], context: ProcessingContext) -> ProcessingResult:
        """执行NER任务"""
        try:
            original_text = input_data.get("original_text", "")
            examples = input_data.get("examples", [])
            
            logger.info(f"🎯 Stage 3: 基于 {len(examples)} 个示例执行NER")
            
            # 格式化示例为上下文
            examples_text = self._format_examples_for_context(examples)
            entity_types = self._get_current_entity_types()
            entity_types_str = ', '.join(entity_types)

            # 构建NER prompt
            ner_prompt = f"""You are an expert Named Entity Recognition system.

Extract named entities from text using ONLY the entity types specified below.

OUTPUT FORMAT REQUIREMENTS:
1. Return ONLY a valid JSON object
2. Keys must be entity types from the label set
3. Values must be arrays of entity strings
4. If no entities found for a type, use empty array []
5. If no entities found at all, return {{}}
6. NO explanations, NO additional text, ONLY JSON

Label set: {entity_types_str}

Examples (learn from these patterns):
{examples_text}

Text to analyze: "{original_text}"

JSON output:"""

            messages = [{"role": "user", "content": ner_prompt}]

            # 调用LLM
            response = await self.model_service.generate_simple_async(
                messages=messages,
                temperature=0.0
            )

            if response:
                # 使用现有的鲁棒JSON解析
                predicted_labels = robust_json_parse_ner(response, entity_types)
                
                logger.info(f"✅ NER完成，识别出 {sum(len(entities) for entities in predicted_labels.values())} 个实体")
                
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    data=predicted_labels,
                    metadata={
                        "stage": "ner",
                        "entities_found": sum(len(entities) for entities in predicted_labels.values()),
                        "examples_used": len(examples)
                    }
                )
            else:
                logger.warning("❌ Stage 3 NER失败：无响应")
                return ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=Exception("No response from model"),
                    metadata={"stage": "ner"}
                )
                
        except Exception as e:
            logger.error(f"❌ Stage 3 NER处理失败: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"stage": "ner"}
            )
    
    def _get_current_entity_types(self) -> List[str]:
        """获取当前数据集的实体类型"""
        current_dataset = get_current_dataset_info()
        return current_dataset.get('labels', ['person', 'organization', 'location'])
    
    def _format_examples_for_context(self, examples) -> str:
        """格式化示例为上下文"""
        if not examples:
            return "No examples available."

        formatted_examples = []
        for i, example in enumerate(examples, 1):
            if hasattr(example, 'example'):
                example_data = example.example
            elif isinstance(example, dict):
                example_data = example
            else:
                example_data = {}
            if not example_data:
                continue

            text = example_data.get('text', '')
            labels = example_data.get('label', {})

            entities_str = self._format_entities(labels)
            formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")

        return "\n\n".join(formatted_examples)
    
    def _format_entities(self, labels: Dict[str, List[str]]) -> str:
        """格式化实体标签"""
        entities = []
        for etype, entities_list in labels.items():
            for entity in entities_list:
                entities.append(f"'{entity}' ({etype})")
        return ", ".join(entities)