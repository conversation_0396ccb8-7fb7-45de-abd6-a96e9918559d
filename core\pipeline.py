#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 处理管道 - 替代MetaCognitiveAgent
模块化、可扩展的处理流程架构
遵循AURA-X原则：统一接口、可适应架构
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Type
from dataclasses import dataclass

from .interfaces import (
    Pipeline, ProcessingResult, ProcessingStatus, ProcessingContext,
    BaseProcessor, FunctionCallHandler, WeightingStrategy
)
from .error_handling import get_error_handler
from .task_manager import get_task_manager
from strategies.json_parsing import MultiLevelJSONParser
from handlers.registry import FunctionCallRegistry

logger = logging.getLogger(__name__)


@dataclass
class PipelineConfig:
    """管道配置"""
    max_workers: int = 10
    enable_caching: bool = True
    enable_error_recovery: bool = True
    default_timeout: float = 300.0
    stage_timeouts: Dict[str, float] = None
    
    def __post_init__(self):
        if self.stage_timeouts is None:
            self.stage_timeouts = {
                "stage1_analysis": 120.0,
                "stage2_retrieval": 300.0,
                "stage3_ner": 180.0
            }


class ProcessingPipeline:
    """
    处理管道 - 新一代的统一处理架构
    替代MetaCognitiveAgent，提供更好的模块化和扩展性
    """
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.stages: Dict[str, BaseProcessor] = {}
        self.stage_order: List[str] = []
        
        # 核心组件
        self.error_handler = get_error_handler()
        self.json_parser = MultiLevelJSONParser()
        self.function_registry = FunctionCallRegistry()
        self.weighting_strategy: Optional[WeightingStrategy] = None
        
        # 统计信息
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "stage_stats": {}
        }
        
        logger.info("🚀 ProcessingPipeline 初始化完成")
    
    def add_stage(self, stage_name: str, processor: BaseProcessor) -> None:
        """添加处理阶段"""
        self.stages[stage_name] = processor
        if stage_name not in self.stage_order:
            self.stage_order.append(stage_name)
        
        # 初始化阶段统计
        if stage_name not in self.execution_stats["stage_stats"]:
            self.execution_stats["stage_stats"][stage_name] = {
                "executions": 0,
                "successes": 0,
                "failures": 0,
                "avg_duration": 0.0
            }
        
        logger.info(f"➕ 添加处理阶段: {stage_name}")
    
    def remove_stage(self, stage_name: str) -> None:
        """移除处理阶段"""
        if stage_name in self.stages:
            del self.stages[stage_name]
        if stage_name in self.stage_order:
            self.stage_order.remove(stage_name)
        logger.info(f"➖ 移除处理阶段: {stage_name}")
    
    def get_stages(self) -> List[str]:
        """获取所有阶段名称"""
        return self.stage_order.copy()
    
    def set_weighting_strategy(self, strategy: WeightingStrategy) -> None:
        """设置加权策略"""
        self.weighting_strategy = strategy
        logger.info("⚖️ 加权策略已设置")
    
    async def execute(self, input_data: Any, context: ProcessingContext = None) -> ProcessingResult:
        """执行完整的处理管道"""
        if context is None:
            context = ProcessingContext(
                sample_id=0,
                stage="pipeline_execution",
                metadata={},
                config={}
            )
        
        self.execution_stats["total_executions"] += 1
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 依次执行各个阶段
            current_data = input_data
            stage_results = {}
            
            for stage_name in self.stage_order:
                if stage_name not in self.stages:
                    logger.warning(f"⚠️ 阶段不存在: {stage_name}")
                    continue
                
                # 更新上下文
                context.stage = stage_name
                context.metadata["current_stage"] = stage_name
                
                # 执行阶段
                stage_result = await self._execute_stage(
                    stage_name, 
                    current_data, 
                    context
                )
                
                stage_results[stage_name] = stage_result
                
                # 检查阶段结果
                if not stage_result.is_success:
                    if stage_result.status == ProcessingStatus.FALLBACK_USED:
                        # 使用降级数据继续
                        current_data = stage_result.fallback_data or stage_result.data
                        logger.info(f"🔄 阶段 {stage_name} 使用降级数据继续")
                    else:
                        # 阶段失败，终止管道
                        self.execution_stats["failed_executions"] += 1
                        return ProcessingResult(
                            status=ProcessingStatus.FAILED,
                            data=None,
                            error=stage_result.error,
                            metadata={
                                "failed_stage": stage_name,
                                "stage_results": stage_results,
                                "duration": asyncio.get_event_loop().time() - start_time
                            }
                        )
                else:
                    # 成功，传递数据到下一阶段
                    current_data = stage_result.data
            
            # 所有阶段完成
            self.execution_stats["successful_executions"] += 1
            return ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                data=current_data,
                metadata={
                    "stage_results": stage_results,
                    "duration": asyncio.get_event_loop().time() - start_time,
                    "stages_executed": len(stage_results)
                }
            )
        
        except Exception as e:
            self.execution_stats["failed_executions"] += 1
            logger.error(f"❌ 管道执行异常: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={
                    "duration": asyncio.get_event_loop().time() - start_time
                }
            )
    
    async def _execute_stage(
        self, 
        stage_name: str, 
        input_data: Any, 
        context: ProcessingContext
    ) -> ProcessingResult:
        """执行单个阶段"""
        processor = self.stages[stage_name]
        stage_stats = self.execution_stats["stage_stats"][stage_name]
        
        stage_stats["executions"] += 1
        start_time = asyncio.get_event_loop().time()
        
        try:
            logger.debug(f"▶️ 执行阶段: {stage_name}")
            
            # 获取阶段超时配置
            timeout = self.config.stage_timeouts.get(stage_name, self.config.default_timeout)
            
            # 使用错误处理器执行
            result = await self.error_handler.execute_with_protection(
                processor.process,
                context,
                f"stage_{stage_name}",
                input_data,
                context
            )
            
            # 更新统计
            duration = asyncio.get_event_loop().time() - start_time
            if result.is_success:
                stage_stats["successes"] += 1
                logger.debug(f"✅ 阶段完成: {stage_name} ({duration:.2f}s)")
            else:
                stage_stats["failures"] += 1
                logger.warning(f"❌ 阶段失败: {stage_name} ({duration:.2f}s)")
            
            # 更新平均执行时间
            stage_stats["avg_duration"] = (
                stage_stats["avg_duration"] * (stage_stats["executions"] - 1) + duration
            ) / stage_stats["executions"]
            
            return result
        
        except Exception as e:
            stage_stats["failures"] += 1
            logger.error(f"❌ 阶段异常: {stage_name}, {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e
            )
    
    async def execute_batch(
        self, 
        inputs: List[Any], 
        base_context: ProcessingContext = None
    ) -> List[ProcessingResult]:
        """批量执行管道"""
        task_manager = await get_task_manager(self.config.max_workers)
        
        # 准备任务
        tasks = []
        for i, input_data in enumerate(inputs):
            context = ProcessingContext(
                sample_id=i,
                stage="batch_execution",
                metadata={"batch_index": i},
                config=base_context.config if base_context else {}
            )
            
            task_info = (
                f"pipeline_task_{i}",
                self.execute,
                (input_data, context),
                {}
            )
            tasks.append(task_info)
        
        # 提交并等待所有任务
        logger.info(f"📦 开始批量执行: {len(inputs)} 个任务")
        completed_tasks = await task_manager.submit_batch(tasks)
        finished_tasks = await task_manager.wait_for_all(
            [task.task_id for task in completed_tasks],
            timeout=self.config.default_timeout * 2
        )
        
        # 收集结果
        results = []
        for task in finished_tasks:
            if task.status.value == "completed":
                results.append(task.result)
            else:
                # 创建失败结果
                error = task.error or Exception("Task failed")
                results.append(ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=error
                ))
        
        logger.info(f"✅ 批量执行完成: {len(results)} 个结果")
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取执行统计信息"""
        return {
            "pipeline_stats": self.execution_stats.copy(),
            "error_stats": self.error_handler.get_error_statistics(),
            "json_parser_stats": self.json_parser.get_success_statistics(),
            "function_registry_stats": self.function_registry.get_statistics()
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "stage_stats": {}
        }
        self.error_handler.reset_error_statistics()
        self.json_parser.reset_statistics()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "pipeline_healthy": True,
            "stages_count": len(self.stages),
            "active_stages": list(self.stages.keys()),
            "error_handler_healthy": True,
            "function_registry_healthy": True,
            "issues": []
        }
        
        # 检查各个阶段
        for stage_name, processor in self.stages.items():
            try:
                # 简单的健康检查（如果处理器支持）
                if hasattr(processor, 'health_check'):
                    await processor.health_check()
            except Exception as e:
                health_status["pipeline_healthy"] = False
                health_status["issues"].append(f"Stage {stage_name} unhealthy: {e}")
        
        # 检查熔断器状态
        for name, circuit_breaker in self.error_handler.circuit_breakers.items():
            if circuit_breaker.state.value != "closed":
                health_status["pipeline_healthy"] = False
                health_status["issues"].append(f"Circuit breaker {name} is {circuit_breaker.state.value}")
        
        return health_status


class NERPipeline(ProcessingPipeline):
    """
    专门为NER任务设计的处理管道
    内置了阶段1（分析）、阶段2（检索）、阶段3（NER）的标准流程
    """
    
    def __init__(self, config: PipelineConfig = None, model_service=None, example_retriever=None):
        super().__init__(config)
        self.model_service = model_service
        self.example_retriever = example_retriever
        
        # 设置JSON解析器的模型服务
        if model_service:
            self.json_parser = MultiLevelJSONParser(model_service)
        
        # 自动添加NER处理阶段
        self._setup_ner_stages()
        
        logger.info("🎯 NERPipeline 初始化完成")
    
    def _setup_ner_stages(self):
        """设置NER专用处理阶段"""
        from .ner_processors import Stage1AnalysisProcessor, Stage2RetrievalProcessor, Stage3NERProcessor
        
        logger.info("🔧 设置NER处理阶段...")
        
        # 添加三个处理阶段
        stage1 = Stage1AnalysisProcessor(self.model_service)
        stage2 = Stage2RetrievalProcessor(self.example_retriever)  
        stage3 = Stage3NERProcessor(self.model_service)
        
        self.add_stage("stage1_analysis", stage1)
        self.add_stage("stage2_retrieval", stage2)
        self.add_stage("stage3_ner", stage3)
        
        logger.info("✅ NER处理阶段设置完成: stage1_analysis -> stage2_retrieval -> stage3_ner")
    
    async def extract_entities(self, text: str, context: ProcessingContext = None) -> Dict[str, List[str]]:
        """
        提取实体 - 兼容原有API的便利方法
        """
        if context is None:
            context = ProcessingContext(
                sample_id=hash(text),
                stage="entity_extraction",
                metadata={"text_length": len(text)},
                config={}
            )
        
        result = await self.execute(text, context)
        
        if result.is_success:
            return result.data or {}
        elif result.status == ProcessingStatus.FALLBACK_USED:
            return result.fallback_data or {}
        else:
            logger.error(f"❌ 实体提取失败: {result.error}")
            return {}


# 全局管道实例管理
_global_pipeline = None

def get_pipeline(model_service=None, example_retriever=None) -> NERPipeline:
    """获取全局NER管道实例"""
    global _global_pipeline
    if _global_pipeline is None:
        _global_pipeline = NERPipeline(
            model_service=model_service,
            example_retriever=example_retriever
        )
    return _global_pipeline