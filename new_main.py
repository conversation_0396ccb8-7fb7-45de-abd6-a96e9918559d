#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 新架构主入口 - 基于模块化管道的NER系统
使用新的ProcessingPipeline替代MetaCognitiveAgent
遵循AURA-X原则：统一、可适应的处理架构
"""

import asyncio
import argparse
import json
import os
import gc
import traceback
from typing import Dict, Any, Optional, List
from datetime import datetime

# 配置和工具
from config import CONFIG, set_dataset, list_available_datasets, get_current_dataset_info, initialize_datasets, get_cache_path
from utils import async_read_json, async_write_json, safe_cancel_tasks, safe_cleanup_tasks, with_timeout, ProgressManager, setup_logging, print_banner

# 新架构组件
from core.pipeline import NERPipeline, PipelineConfig
from core.error_handling import get_error_handler, CircuitBreakerConfig
from core.interfaces import ProcessingContext
from strategies.weighting import WeightingConfig, create_weighting_strategy
from handlers.registry import get_function_call_registry
from handlers.ner_retrieval import NERRetrievalHandler

# 服务组件
from model_interface import model_service
from example_retriever import example_retriever


async def process_and_eval_dataset_new_architecture(max_samples: Optional[int] = None) -> Dict[str, Any]:
    """🎯 使用新架构的三阶段统一处理数据集"""
    
    # 创建进度管理器
    progress = ProgressManager()
    
    # 获取当前数据集信息
    current_dataset = get_current_dataset_info()
    dataset_path = current_dataset['path']
    
    # 检查测试集文件
    test_path = dataset_path.replace('train.json', 'test.json')
    if not os.path.exists(test_path):
        print(f"❌ 测试集文件不存在: {test_path}")
        return {}
    
    # 阶段0：数据准备和新架构初始化
    progress.start_stage("🚀 阶段0：数据准备和新架构初始化", 4)
    
    try:
        test_data = await async_read_json(test_path)
        progress.update_progress(completed=1)
    except Exception as e:
        print(f"❌ 加载测试集失败: {e}")
        return {}
    
    # 限制样本数量
    if max_samples and max_samples < len(test_data):
        test_data = test_data[:max_samples]
    progress.update_progress(completed=2)
    
    # 初始化向量库
    progress.log_message("🔍 初始化向量库...")
    vector_ready = await example_retriever.initialize_vector_store()
    if vector_ready:
        progress.log_message("✅ 向量库初始化成功")
    else:
        progress.log_message("⚠️ 向量库初始化失败，将使用直接模式")
    progress.update_progress(completed=3)
    
    # 初始化新架构组件
    progress.log_message("🚀 初始化新架构组件...")
    
    # 1. 配置错误处理
    error_handler = get_error_handler()
    error_handler.register_circuit_breaker(
        "ner_processing",
        CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
    )
    
    # 2. 注册FunctionCall处理器
    registry = get_function_call_registry()
    ner_handler = NERRetrievalHandler(
        example_retriever=example_retriever,
        model_service=model_service
    )
    registry.register_handler(ner_handler, priority=10)
    
    # 3. 配置权重策略
    weighting_config = WeightingConfig(
        similarity_weight=0.5,
        diversity_weight=0.3,
        recency_weight=0.1,
        domain_relevance_weight=0.1
    )
    weighting_strategy = create_weighting_strategy("standard", weighting_config)
    
    # 4. 创建处理管道
    pipeline_config = PipelineConfig(
        max_workers=10,
        enable_caching=True,
        enable_error_recovery=True,
        default_timeout=300.0
    )
    
    pipeline = NERPipeline(
        config=pipeline_config,
        model_service=model_service,
        example_retriever=example_retriever
    )
    pipeline.set_weighting_strategy(weighting_strategy)
    
    progress.update_progress(completed=4)
    progress.finish_stage("新架构初始化完成")
    
    # 阶段1-3：使用新架构的统一处理
    progress.start_stage("🎯 新架构统一处理", len(test_data))
    
    # 检查缓存
    cache_file = get_cache_path(f"new_arch_results_{len(test_data)}")
    
    if os.path.exists(cache_file):
        progress.log_message("📦 发现新架构结果缓存，正在加载...")
        try:
            cached_results = await async_read_json(cache_file)
            progress.log_message(f"✅ 从缓存加载了 {len(cached_results)} 个结果")
            results = cached_results
            progress.update_progress(completed=len(cached_results))
            progress.finish_stage(f"统一处理完成 - 从缓存加载: {len(cached_results)}")
        except (json.JSONDecodeError, KeyError) as e:
            progress.log_message(f"⚠️ 缓存文件损坏，重新处理... 错误: {e}")
            results = None
    else:
        results = None
    
    if results is None:
        batch_size = CONFIG.get('batch_size', 64)
        batch_delay = CONFIG.get('batch_delay', 1.0)
        
        async def process_single_sample(i, sample):
            """使用新架构处理单个样本"""
            text = sample.get('text', '')
            true_labels = sample.get('label', {})
            
            try:
                # 创建处理上下文
                context = ProcessingContext(
                    sample_id=i,
                    stage="unified_ner_processing",
                    metadata={
                        "text_length": len(text),
                        "use_enhanced_parsing": True,
                        "domain_keywords": ["person", "organization", "location"]
                    },
                    config={
                        "timeout": 300.0,
                        "enable_fallback": True,
                        "max_retries": 2
                    }
                )
                
                # 使用新架构执行完整的NER流程
                result = await pipeline.execute(text, context)
                
                if result.is_success:
                    predicted_labels = result.data or {}
                elif result.status.value == "fallback_used":
                    predicted_labels = result.fallback_data or {}
                else:
                    progress.log_message(f"⚠️ 样本 {i} 处理失败: {result.error}")
                    predicted_labels = {}
                
            except Exception as e:
                progress.log_message(f"⚠️ 样本 {i} 处理异常: {e}")
                predicted_labels = {}
            
            if not isinstance(predicted_labels, dict):
                predicted_labels = {}
            
            # 计算指标
            sample_correct = 0
            sample_total = sum(len(entities) for entities in true_labels.values())
            sample_predicted = sum(len(entities) for entities in predicted_labels.values())
            
            for entity_type, true_entities in true_labels.items():
                predicted_entities_of_type = predicted_labels.get(entity_type, [])
                for entity in true_entities:
                    if entity in predicted_entities_of_type:
                        sample_correct += 1
            
            return {
                'text': text,
                'true_labels': true_labels,
                'predicted_labels': predicted_labels,
                'correct': sample_correct,
                'total_true': sample_total,
                'total_predicted': sample_predicted
            }
        
        # 批量处理
        all_tasks = []
        
        for i in range(0, len(test_data), batch_size):
            batch_samples = test_data[i:i+batch_size]
            batch_indices = list(range(i, min(i+batch_size, len(test_data))))
            
            # 创建批次任务
            batch_tasks = [
                process_single_sample(idx, sample)
                for idx, sample in zip(batch_indices, batch_samples)
            ]
            
            all_tasks.extend(batch_tasks)
            
            # 批次间延迟
            if i + batch_size < len(test_data):
                await asyncio.sleep(batch_delay)
        
        # 执行所有任务
        tasks = [asyncio.create_task(coro) for coro in all_tasks]
        all_results = []
        completed_count = 0
        
        try:
            timeout = CONFIG.get('timeouts', {}).get('stage3_batch', 1800)
            for completed_task in asyncio.as_completed(tasks, timeout=timeout):
                try:
                    result = await completed_task
                    all_results.append(result)
                except Exception as e:
                    all_results.append(e)
                
                completed_count += 1
                progress.update_progress(completed=completed_count)
        
        except asyncio.TimeoutError:
            progress.log_message("⚠️ 处理任务超时，使用部分结果继续")
            await safe_cancel_tasks(tasks)
            if len(all_results) < len(tasks):
                all_results.extend([Exception("Timeout")] * (len(tasks) - len(all_results)))
        
        finally:
            await safe_cleanup_tasks(tasks)
        
        # 处理结果
        results = []
        success_count = 0
        failed_count = 0
        
        for result in all_results:
            if isinstance(result, Exception):
                progress.log_message(f"⚠️ 处理任务失败: {result}")
                failed_count += 1
            else:
                results.append(result)
                success_count += 1
        
        progress.update_progress(success=success_count, failed=failed_count)
        progress.finish_stage(f"统一处理完成 - 成功: {len(results)}/{len(test_data)}")
        
        # 清理内存
        del all_tasks, tasks, all_results
        gc.collect()
        
        # 保存到缓存
        try:
            await async_write_json(cache_file, results)
            progress.log_message(f"💾 新架构结果已缓存到: {cache_file}")
        except Exception as e:
            progress.log_message(f"⚠️ 缓存保存失败: {e}")
    
    # 计算汇总指标
    correct_predictions = sum(r['correct'] for r in results)
    total_entities = sum(r['total_true'] for r in results)
    predicted_entities = sum(r['total_predicted'] for r in results)
    
    print()
    
    # 计算最终指标
    precision = correct_predictions / predicted_entities if predicted_entities > 0 else 0
    recall = correct_predictions / total_entities if total_entities > 0 else 0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
    
    # 显示最终评估结果
    print("\n" + "="*60)
    print("🚀 新架构评估结果")
    print("="*60)
    print(f"📊 数据集: {current_dataset['name']}")
    print(f"📝 处理样本数: {len(test_data)}")
    print(f"🎯 真实实体总数: {total_entities}")
    print(f"🔍 预测实体总数: {predicted_entities}")
    print(f"✅ 正确预测数: {correct_predictions}")
    print("-" * 40)
    print(f"📈 Precision: {precision:.4f} ({correct_predictions}/{predicted_entities})")
    print(f"📈 Recall: {recall:.4f} ({correct_predictions}/{total_entities})")
    print(f"📈 F1-Score: {f1_score:.4f}")
    print("="*60)
    
    # 获取管道统计信息
    pipeline_stats = pipeline.get_statistics()
    print(f"🔧 管道统计: {pipeline_stats}")
    
    # 保存评估结果
    eval_results = {
        'dataset': current_dataset['name'],
        'timestamp': datetime.now().isoformat(),
        'samples_count': len(test_data),
        'total_entities': total_entities,
        'predicted_entities': predicted_entities,
        'correct_predictions': correct_predictions,
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'processing_mode': 'new_architecture_unified',
        'pipeline_stats': pipeline_stats,
        'detailed_results': results
    }
    
    # 保存到文件
    results_dir = CONFIG.get('results_dir', './results')
    os.makedirs(results_dir, exist_ok=True)
    eval_file = os.path.join(results_dir, f"eval_results_new_arch_{current_dataset['name'].lower().replace(' ', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    try:
        await async_write_json(eval_file, eval_results)
        print(f"💾 详细结果已保存到: {eval_file}")
    except Exception as e:
        print(f"⚠️ 保存结果失败: {e}")
    
    return eval_results


async def main():
    """主函数 - 新架构统一处理"""
    parser = argparse.ArgumentParser(description='🚀 新架构NER系统 - 基于模块化管道')
    parser.add_argument('--dataset', '-d', type=str, default=CONFIG.get('dataset', 'ace2005'),
                       help=f'数据集名称 (默认: {CONFIG.get("dataset", "ace2005")})')
    parser.add_argument('--max-samples', type=int, default=CONFIG.get('max_test_samples'),
                       help=f'最大测试样本数 (默认: {CONFIG.get("max_test_samples", "处理全部")})')
    parser.add_argument('--log-level', type=str, default=CONFIG.get('log_level', 'WARNING'),
                       help=f'日志级别 (默认: {CONFIG.get("log_level", "WARNING")}) (DEBUG/INFO/WARNING/ERROR)')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging(args.log_level)
    
    # 初始化数据集配置
    initialize_datasets()
    
    # 打印横幅
    print_banner()
    print("🚀 使用新架构模块化管道")
    
    # 设置数据集
    if not set_dataset(args.dataset):
        print(f"❌ 数据集不存在: {args.dataset}")
        available = list_available_datasets()
        print("\n可用数据集:")
        for key, info in available.items():
            status = "✅" if info['available'] else "❌"
            current = "👈 当前" if info['current'] else ""
            print(f"  {status} {key}: {info['name']} {current}")
        return
    
    # 显示当前配置
    current_dataset = get_current_dataset_info()
    print(f"📊 数据集: {current_dataset['name']}")
    if args.max_samples:
        print(f"📝 最大样本数: {args.max_samples}")
    print()
    
    try:
        # 执行新架构统一处理
        await process_and_eval_dataset_new_architecture(args.max_samples)
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
