#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 JSON解析策略
多级降级解析策略，解决阶段1 LLM返回格式不一致的问题
遵循AURA-X原则：智能降级和自动修复
"""

import json
import re
import logging
from typing import Any, Dict, List, Optional, Union
from abc import ABC, abstractmethod

from core.interfaces import ProcessingStrategy, ProcessingResult, ProcessingStatus, ProcessingContext

logger = logging.getLogger(__name__)


class JSONParsingStrategy(ProcessingStrategy):
    """JSON解析策略基类"""
    
    def __init__(self, name: str):
        self.name = name
    
    @abstractmethod
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """解析JSON字符串"""
        pass
    
    async def process(self, input_data: str, context: ProcessingContext) -> ProcessingResult:
        """实现ProcessingStrategy接口"""
        return await self.parse_json(input_data, context)
    
    def can_handle(self, input_data: Any, context: ProcessingContext) -> bool:
        """判断是否能处理给定输入"""
        return isinstance(input_data, str)


class DirectJSONParser(JSONParsingStrategy):
    """直接JSON解析器 - 最快速的解析方式"""
    
    def __init__(self):
        super().__init__("DirectJSONParser")
    
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """直接尝试解析JSON"""
        try:
            data = json.loads(json_str.strip())
            logger.debug(f"✅ 直接JSON解析成功: {self.name}")
            return ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                data=data,
                metadata={"parser": self.name}
            )
        except json.JSONDecodeError as e:
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"parser": self.name, "error_msg": str(e)}
            )


class CleaningJSONParser(JSONParsingStrategy):
    """清理式JSON解析器 - 清理常见格式问题"""
    
    def __init__(self):
        super().__init__("CleaningJSONParser")
    
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """清理后解析JSON"""
        try:
            cleaned_json = self._clean_json_string(json_str)
            data = json.loads(cleaned_json)
            logger.debug(f"✅ 清理JSON解析成功: {self.name}")
            return ProcessingResult(
                status=ProcessingStatus.SUCCESS,
                data=data,
                metadata={"parser": self.name, "cleaned": True}
            )
        except json.JSONDecodeError as e:
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"parser": self.name, "error_msg": str(e)}
            )
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串"""
        # 移除markdown代码块
        cleaned = re.sub(r'```json\s*', '', json_str)
        cleaned = re.sub(r'```', '', cleaned)
        
        # 提取JSON部分
        json_match = re.search(r'\{.*\}', cleaned, re.DOTALL)
        if json_match:
            cleaned = json_match.group()
        
        # 修复常见错误
        cleaned = cleaned.replace("'", '"')  # 单引号转双引号
        cleaned = re.sub(r',\s*}', '}', cleaned)  # 移除尾随逗号
        cleaned = re.sub(r',\s*]', ']', cleaned)  # 移除数组尾随逗号
        
        # 修复缺少引号的键
        cleaned = re.sub(r'(\w+):', r'"\1":', cleaned)
        
        return cleaned.strip()


class RegexJSONParser(JSONParsingStrategy):
    """正则表达式JSON解析器 - 基于模式提取"""
    
    def __init__(self):
        super().__init__("RegexJSONParser")
    
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """使用正则表达式提取JSON字段"""
        try:
            extracted_data = self._extract_fields_by_regex(json_str)
            if extracted_data:
                logger.debug(f"✅ 正则JSON解析成功: {self.name}")
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    data=extracted_data,
                    metadata={"parser": self.name, "method": "regex_extraction"}
                )
            else:
                return ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=Exception("No valid fields extracted"),
                    metadata={"parser": self.name}
                )
        except Exception as e:
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"parser": self.name, "error_msg": str(e)}
            )
    
    def _extract_fields_by_regex(self, text: str) -> Optional[Dict[str, Any]]:
        """使用正则表达式提取已知字段"""
        result = {}
        
        # 提取 description 字段
        desc_patterns = [
            r'"description"\s*:\s*"([^"]*)"',
            r"'description'\s*:\s*'([^']*)'",
            r'description\s*:\s*"([^"]*)"',
            r'description\s*:\s*\'([^\']*)\'',
        ]
        
        for pattern in desc_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                result["description"] = match.group(1)
                break
        
        # 提取 k 字段
        k_patterns = [
            r'"k"\s*:\s*(\d+)',
            r"'k'\s*:\s*(\d+)",
            r'k\s*:\s*(\d+)',
        ]
        
        for pattern in k_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                result["k"] = int(match.group(1))
                break
        
        return result if result else None


class LLMAssistedJSONParser(JSONParsingStrategy):
    """LLM辅助JSON解析器 - 让LLM修复格式问题"""
    
    def __init__(self, model_service=None):
        super().__init__("LLMAssistedJSONParser")
        self.model_service = model_service
    
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """使用LLM修复JSON格式"""
        if not self.model_service:
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=Exception("Model service not available"),
                metadata={"parser": self.name}
            )
        
        try:
            # 构建修复prompt
            fix_prompt = f"""The following text should be a valid JSON object but has formatting issues. 
Please fix it and return only the valid JSON:

{json_str}

Return only the corrected JSON, no explanations:"""
            
            messages = [{"role": "user", "content": fix_prompt}]
            response = await self.model_service.generate_simple_async(
                messages=messages,
                temperature=0.0,
                max_tokens=200
            )
            
            if response:
                # 尝试解析修复后的JSON
                fixed_data = json.loads(response.strip())
                logger.debug(f"✅ LLM辅助JSON解析成功: {self.name}")
                return ProcessingResult(
                    status=ProcessingStatus.SUCCESS,
                    data=fixed_data,
                    metadata={"parser": self.name, "llm_fixed": True}
                )
            
        except Exception as e:
            logger.debug(f"❌ LLM辅助JSON解析失败: {e}")
        
        return ProcessingResult(
            status=ProcessingStatus.FAILED,
            data=None,
            error=Exception("LLM assisted parsing failed"),
            metadata={"parser": self.name}
        )


class FallbackJSONParser(JSONParsingStrategy):
    """降级JSON解析器 - 返回默认值"""
    
    def __init__(self, default_values: Dict[str, Any] = None):
        super().__init__("FallbackJSONParser")
        self.default_values = default_values or {
            "description": "general NER examples",
            "k": 3
        }
    
    async def parse_json(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """返回默认值"""
        logger.warning(f"🔄 使用降级JSON解析器，返回默认值: {self.default_values}")
        return ProcessingResult(
            status=ProcessingStatus.FALLBACK_USED,
            data=self.default_values.copy(),
            fallback_data=self.default_values.copy(),
            metadata={"parser": self.name, "fallback": True}
        )


class MultiLevelJSONParser:
    """多级JSON解析器 - 组合多种解析策略"""
    
    def __init__(self, model_service=None):
        self.strategies: List[JSONParsingStrategy] = [
            DirectJSONParser(),
            CleaningJSONParser(),
            RegexJSONParser(),
            LLMAssistedJSONParser(model_service),
            FallbackJSONParser()
        ]
        self.success_stats: Dict[str, int] = {}
    
    async def parse(self, json_str: str, context: ProcessingContext) -> ProcessingResult:
        """依次尝试多种解析策略"""
        if not json_str or not json_str.strip():
            logger.warning("⚠️ 空JSON字符串，使用降级解析")
            return await self.strategies[-1].parse_json(json_str, context)
        
        for strategy in self.strategies:
            try:
                result = await strategy.parse_json(json_str, context)
                
                if result.is_success:
                    # 记录成功统计
                    strategy_name = strategy.name
                    self.success_stats[strategy_name] = self.success_stats.get(strategy_name, 0) + 1
                    
                    # 验证必要字段
                    if self._validate_parsed_data(result.data):
                        logger.debug(f"✅ JSON解析成功: {strategy_name}")
                        return result
                    else:
                        logger.debug(f"⚠️ 解析结果验证失败: {strategy_name}")
                        continue
                
                elif result.status == ProcessingStatus.FALLBACK_USED:
                    # 降级解析直接返回
                    return result
                
            except Exception as e:
                logger.debug(f"❌ 解析策略失败 {strategy.name}: {e}")
                continue
        
        # 所有策略都失败，返回最后一个结果（通常是降级结果）
        return ProcessingResult(
            status=ProcessingStatus.FAILED,
            data=None,
            error=Exception("All JSON parsing strategies failed"),
            metadata={"attempted_strategies": [s.name for s in self.strategies]}
        )
    
    def _validate_parsed_data(self, data: Any) -> bool:
        """验证解析后的数据是否符合要求"""
        if not isinstance(data, dict):
            return False
        
        # 检查必要字段
        required_fields = ["description", "k"]
        for field in required_fields:
            if field not in data:
                return False
        
        # 检查字段类型
        if not isinstance(data.get("description"), str):
            return False
        
        if not isinstance(data.get("k"), int) or data.get("k") <= 0:
            return False
        
        return True
    
    def get_success_statistics(self) -> Dict[str, int]:
        """获取成功解析统计"""
        return self.success_stats.copy()
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.success_stats.clear()


# 工具函数 - 向后兼容
async def parse_tool_arguments_enhanced(
    arguments_str: str, 
    context: ProcessingContext = None,
    model_service=None
) -> Optional[Dict[str, Any]]:
    """
    增强的工具参数解析函数 - 替代utils.py中的parse_tool_arguments
    """
    if context is None:
        context = ProcessingContext(
            sample_id=0,
            stage="json_parsing",
            metadata={},
            config={}
        )
    
    parser = MultiLevelJSONParser(model_service)
    result = await parser.parse(arguments_str, context)
    
    if result.is_success or result.status == ProcessingStatus.FALLBACK_USED:
        return result.data or result.fallback_data
    
    return None