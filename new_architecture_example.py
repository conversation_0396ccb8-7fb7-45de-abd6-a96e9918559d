#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 新架构使用示例
展示如何使用重构后的模块化架构
遵循AURA-X原则的完整使用示例
"""

import asyncio
import logging
from typing import Dict, List

# 核心组件
from core.pipeline import NERPipeline, PipelineConfig
from core.error_handling import get_error_handler, CircuitBreakerConfig
from core.task_manager import get_task_manager
from core.interfaces import ProcessingContext

# 策略组件
from strategies.weighting import WeightingConfig, create_weighting_strategy
from strategies.json_parsing import MultiLevelJSONParser

# 处理器组件
from handlers.registry import get_function_call_registry
from handlers.ner_retrieval import NERRetrievalHandler

logger = logging.getLogger(__name__)


class NewArchitectureDemo:
    """新架构演示类"""
    
    def __init__(self, model_service=None, example_retriever=None):
        self.model_service = model_service
        self.example_retriever = example_retriever
        self.pipeline = None
        self.initialized = False
    
    async def initialize(self):
        """初始化新架构组件"""
        logger.info("🚀 初始化新架构组件...")
        
        # 1. 配置错误处理
        error_handler = get_error_handler()
        error_handler.register_circuit_breaker(
            "ner_processing",
            CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30.0)
        )
        
        # 2. 注册FunctionCall处理器
        registry = get_function_call_registry()
        ner_handler = NERRetrievalHandler(
            example_retriever=self.example_retriever,
            model_service=self.model_service
        )
        registry.register_handler(ner_handler, priority=10)
        
        # 3. 配置权重策略
        weighting_config = WeightingConfig(
            similarity_weight=0.5,
            diversity_weight=0.3,
            recency_weight=0.1,
            domain_relevance_weight=0.1
        )
        weighting_strategy = create_weighting_strategy("standard", weighting_config)
        
        # 4. 创建处理管道
        pipeline_config = PipelineConfig(
            max_workers=10,
            enable_caching=True,
            enable_error_recovery=True,
            default_timeout=300.0
        )
        
        self.pipeline = NERPipeline(
            config=pipeline_config,
            model_service=self.model_service,
            example_retriever=self.example_retriever
        )
        self.pipeline.set_weighting_strategy(weighting_strategy)
        
        self.initialized = True
        logger.info("✅ 新架构初始化完成")
    
    async def extract_entities_new_way(self, text: str) -> Dict[str, List[str]]:
        """
        使用新架构提取实体 - 演示完整流程
        解决了原有的阶段1和阶段2问题
        """
        if not self.initialized:
            await self.initialize()
        
        # 创建处理上下文
        context = ProcessingContext(
            sample_id=hash(text),
            stage="entity_extraction",
            metadata={
                "text_length": len(text),
                "use_enhanced_parsing": True,
                "domain_keywords": ["person", "organization", "location"]
            },
            config={
                "timeout": 180.0,
                "enable_fallback": True,
                "max_retries": 2
            }
        )
        
        # 执行处理管道
        result = await self.pipeline.execute(text, context)
        
        if result.is_success:
            logger.info("✅ 新架构实体提取成功")
            return result.data or {}
        elif result.status.value == "fallback_used":
            logger.warning("🔄 使用降级结果")
            return result.fallback_data or {}
        else:
            logger.error(f"❌ 实体提取失败: {result.error}")
            return {}
    
    async def batch_extract_entities(self, texts: List[str]) -> List[Dict[str, List[str]]]:
        """批量实体提取 - 演示批处理能力"""
        if not self.initialized:
            await self.initialize()
        
        logger.info(f"📦 开始批量实体提取: {len(texts)} 个文本")
        
        # 创建基础上下文
        base_context = ProcessingContext(
            sample_id=0,
            stage="batch_extraction",
            metadata={"batch_size": len(texts)},
            config={"timeout": 300.0, "enable_fallback": True}
        )
        
        # 执行批处理
        results = await self.pipeline.execute_batch(texts, base_context)
        
        # 提取实体结果
        entities_list = []
        for result in results:
            if result.is_success:
                entities_list.append(result.data or {})
            elif result.status.value == "fallback_used":
                entities_list.append(result.fallback_data or {})
            else:
                entities_list.append({})
        
        logger.info(f"✅ 批量提取完成: {len(entities_list)} 个结果")
        return entities_list
    
    async def demonstrate_json_parsing_robustness(self):
        """演示JSON解析的鲁棒性 - 解决阶段1问题"""
        logger.info("🔧 演示JSON解析鲁棒性...")
        
        # 测试各种有问题的JSON格式
        problematic_jsons = [
            '{"description": "news text with entities", "k": 3}',  # 正常格式
            "{'description': 'news text', 'k': 3}",  # 单引号问题
            '```json\n{"description": "code block", "k": 2}\n```',  # markdown包装
            '{"description": "trailing comma", "k": 4,}',  # 尾随逗号
            'description: "missing quotes", k: 2',  # 缺少引号
            'completely broken format',  # 完全错误格式
        ]
        
        parser = MultiLevelJSONParser(self.model_service)
        
        for i, json_str in enumerate(problematic_jsons):
            context = ProcessingContext(
                sample_id=i,
                stage="json_parsing_test",
                metadata={},
                config={}
            )
            
            result = await parser.parse(json_str, context)
            
            if result.is_success:
                logger.info(f"✅ JSON {i+1} 解析成功: {result.data}")
            elif result.status.value == "fallback_used":
                logger.info(f"🔄 JSON {i+1} 使用降级: {result.fallback_data}")
            else:
                logger.warning(f"❌ JSON {i+1} 解析失败: {result.error}")
    
    async def demonstrate_error_recovery(self):
        """演示错误恢复机制"""
        logger.info("🛡️ 演示错误恢复机制...")
        
        error_handler = get_error_handler()
        
        # 模拟一个经常失败的函数
        async def unreliable_function(fail_count: int = 0):
            if fail_count > 0:
                raise Exception(f"模拟失败 {fail_count}")
            return "成功执行"
        
        context = ProcessingContext(
            sample_id=1,
            stage="error_recovery_test",
            metadata={},
            config={}
        )
        
        # 测试重试机制
        for fail_count in [2, 1, 0]:  # 逐渐减少失败次数
            result = await error_handler.execute_with_protection(
                unreliable_function,
                context,
                circuit_breaker_name="test_breaker",
                fail_count=fail_count
            )
            
            logger.info(f"失败{fail_count}次的测试 - 状态: {result.status.value}")
    
    async def get_comprehensive_statistics(self) -> Dict:
        """获取全面的统计信息"""
        if not self.initialized:
            return {"error": "未初始化"}
        
        stats = {
            "pipeline_stats": self.pipeline.get_statistics(),
            "registry_stats": get_function_call_registry().get_statistics(),
            "error_handler_stats": get_error_handler().get_error_statistics(),
            "task_manager_stats": (await get_task_manager()).get_statistics()
        }
        
        return stats
    
    async def health_check(self) -> Dict:
        """全面健康检查"""
        if not self.initialized:
            return {"healthy": False, "reason": "未初始化"}
        
        health_results = {
            "pipeline_health": await self.pipeline.health_check(),
            "registry_health": await get_function_call_registry().health_check(),
            "overall_healthy": True
        }
        
        # 检查整体健康状态
        for component, health in health_results.items():
            if isinstance(health, dict) and not health.get("pipeline_healthy", health.get("registry_healthy", True)):
                health_results["overall_healthy"] = False
                break
        
        return health_results


async def main_demo():
    """主演示函数"""
    logging.basicConfig(level=logging.INFO)
    
    # 注意：这里需要实际的model_service和example_retriever实例
    # demo = NewArchitectureDemo(model_service=your_model_service, example_retriever=your_retriever)
    demo = NewArchitectureDemo()  # 演示版本
    
    try:
        # 演示JSON解析鲁棒性
        await demo.demonstrate_json_parsing_robustness()
        
        # 演示错误恢复
        await demo.demonstrate_error_recovery()
        
        # 如果有实际的服务，可以测试实体提取
        # entities = await demo.extract_entities_new_way("Apple Inc. was founded in California.")
        # print(f"提取的实体: {entities}")
        
        # 健康检查
        health = await demo.health_check()
        logger.info(f"系统健康状态: {health}")
        
        # 统计信息
        stats = await demo.get_comprehensive_statistics()
        logger.info(f"系统统计: {stats}")
        
    except Exception as e:
        logger.error(f"演示异常: {e}")


if __name__ == "__main__":
    asyncio.run(main_demo())