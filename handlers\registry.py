#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📋 处理器注册管理
管理所有FunctionCall处理器的注册和调度
遵循AURA-X原则：统一管理、可扩展架构
"""

import logging
from typing import Dict, List, Optional, Any

from core.interfaces import FunctionCallHandler, ProcessingResult, ProcessingContext, ToolCall, ProcessingStatus
from .base import BaseFunctionCallHandler

logger = logging.getLogger(__name__)


class FunctionCallRegistry:
    """FunctionCall处理器注册中心"""
    
    def __init__(self):
        self.handlers: Dict[str, FunctionCallHandler] = {}
        self.tool_to_handler: Dict[str, str] = {}  # 工具名到处理器名的映射
        self.handler_priorities: Dict[str, int] = {}  # 处理器优先级
        
        # 统计信息
        self.call_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "handler_usage": {}
        }
        
        logger.info("📋 FunctionCall注册中心初始化完成")
    
    def register_handler(
        self, 
        handler: FunctionCallHandler, 
        priority: int = 0,
        override: bool = False
    ) -> bool:
        """
        注册处理器
        
        Args:
            handler: 处理器实例
            priority: 优先级（数字越大优先级越高）
            override: 是否覆盖已存在的处理器
        """
        handler_name = getattr(handler, 'name', handler.__class__.__name__)
        
        # 检查是否已存在
        if handler_name in self.handlers and not override:
            logger.warning(f"⚠️ 处理器已存在: {handler_name}，使用override=True强制覆盖")
            return False
        
        # 注册处理器
        self.handlers[handler_name] = handler
        self.handler_priorities[handler_name] = priority
        
        # 注册支持的工具
        for tool_name in handler.supported_tools:
            # 检查工具是否已被其他处理器注册
            if tool_name in self.tool_to_handler:
                existing_handler = self.tool_to_handler[tool_name]
                existing_priority = self.handler_priorities.get(existing_handler, 0)
                
                # 如果新处理器优先级更高，则替换
                if priority > existing_priority:
                    logger.info(f"🔄 工具 {tool_name} 处理器更新: {existing_handler} -> {handler_name}")
                    self.tool_to_handler[tool_name] = handler_name
                else:
                    logger.info(f"ℹ️ 工具 {tool_name} 保持现有处理器: {existing_handler} (优先级: {existing_priority})")
            else:
                self.tool_to_handler[tool_name] = handler_name
        
        # 初始化统计
        if handler_name not in self.call_stats["handler_usage"]:
            self.call_stats["handler_usage"][handler_name] = {
                "calls": 0,
                "successes": 0,
                "failures": 0
            }
        
        logger.info(f"✅ 处理器注册成功: {handler_name} (优先级: {priority}, 工具: {handler.supported_tools})")
        return True
    
    def unregister_handler(self, handler_name: str) -> bool:
        """注销处理器"""
        if handler_name not in self.handlers:
            logger.warning(f"⚠️ 处理器不存在: {handler_name}")
            return False
        
        handler = self.handlers[handler_name]
        
        # 移除工具映射
        tools_to_remove = []
        for tool_name, mapped_handler in self.tool_to_handler.items():
            if mapped_handler == handler_name:
                tools_to_remove.append(tool_name)
        
        for tool_name in tools_to_remove:
            del self.tool_to_handler[tool_name]
        
        # 移除处理器
        del self.handlers[handler_name]
        if handler_name in self.handler_priorities:
            del self.handler_priorities[handler_name]
        
        logger.info(f"🗑️ 处理器已注销: {handler_name}")
        return True
    
    def get_handler(self, tool_name: str) -> Optional[FunctionCallHandler]:
        """根据工具名获取处理器"""
        handler_name = self.tool_to_handler.get(tool_name)
        if handler_name:
            return self.handlers.get(handler_name)
        return None
    
    def list_available_tools(self) -> List[str]:
        """列出所有可用工具"""
        return list(self.tool_to_handler.keys())
    
    def list_handlers(self) -> List[str]:
        """列出所有注册的处理器"""
        return list(self.handlers.keys())
    
    async def handle_tool_call(self, tool_call: Any, context: ProcessingContext) -> ProcessingResult:
        """处理工具调用的统一入口"""
        self.call_stats["total_calls"] += 1
        
        try:
            # 标准化工具调用
            if hasattr(tool_call, 'function'):
                tool_name = tool_call.function.name
            elif isinstance(tool_call, dict):
                tool_name = tool_call.get('tool_name', '')
            elif hasattr(tool_call, 'tool_name'):
                tool_name = tool_call.tool_name
            else:
                raise ValueError(f"无法识别的工具调用格式: {type(tool_call)}")
            
            # 查找处理器
            handler = self.get_handler(tool_name)
            if not handler:
                error_msg = f"未找到工具处理器: {tool_name}"
                logger.error(f"❌ {error_msg}")
                self.call_stats["failed_calls"] += 1
                return ProcessingResult(
                    status=ProcessingStatus.FAILED,
                    data=None,
                    error=Exception(error_msg),
                    metadata={"registry": "FunctionCallRegistry", "tool_name": tool_name}
                )
            
            # 执行处理
            handler_name = getattr(handler, 'name', handler.__class__.__name__)
            logger.debug(f"🔧 使用处理器 {handler_name} 处理工具 {tool_name}")
            
            result = await handler.handle(tool_call, context)
            
            # 更新统计
            handler_stats = self.call_stats["handler_usage"].get(handler_name, {
                "calls": 0, "successes": 0, "failures": 0
            })
            handler_stats["calls"] += 1
            
            if result.is_success:
                self.call_stats["successful_calls"] += 1
                handler_stats["successes"] += 1
                logger.debug(f"✅ 工具调用成功: {tool_name}")
            else:
                self.call_stats["failed_calls"] += 1
                handler_stats["failures"] += 1
                logger.warning(f"❌ 工具调用失败: {tool_name}, 错误: {result.error}")
            
            self.call_stats["handler_usage"][handler_name] = handler_stats
            return result
            
        except Exception as e:
            self.call_stats["failed_calls"] += 1
            logger.error(f"❌ 工具调用处理异常: {e}")
            return ProcessingResult(
                status=ProcessingStatus.FAILED,
                data=None,
                error=e,
                metadata={"registry": "FunctionCallRegistry"}
            )
    
    async def handle_multiple_tool_calls(
        self, 
        tool_calls: List[Any], 
        context: ProcessingContext
    ) -> List[ProcessingResult]:
        """处理多个工具调用"""
        results = []
        
        for i, tool_call in enumerate(tool_calls):
            # 更新上下文
            call_context = ProcessingContext(
                sample_id=context.sample_id,
                stage=f"{context.stage}_call_{i}",
                metadata={**context.metadata, "call_index": i},
                config=context.config,
                retry_count=context.retry_count,
                error_history=context.error_history.copy()
            )
            
            result = await self.handle_tool_call(tool_call, call_context)
            results.append(result)
        
        return results
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取注册中心统计信息"""
        return {
            "total_handlers": len(self.handlers),
            "total_tools": len(self.tool_to_handler),
            "call_statistics": self.call_stats.copy(),
            "handler_details": {
                name: {
                    "supported_tools": handler.supported_tools,
                    "priority": self.handler_priorities.get(name, 0),
                    "individual_stats": handler.get_statistics() if isinstance(handler, BaseFunctionCallHandler) else {}
                }
                for name, handler in self.handlers.items()
            }
        }
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.call_stats = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "handler_usage": {}
        }
        
        # 重置各个处理器的统计
        for handler in self.handlers.values():
            if isinstance(handler, BaseFunctionCallHandler):
                handler.reset_statistics()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            "registry_healthy": True,
            "total_handlers": len(self.handlers),
            "healthy_handlers": 0,
            "unhealthy_handlers": [],
            "handler_status": {}
        }
        
        for name, handler in self.handlers.items():
            try:
                if hasattr(handler, 'health_check'):
                    is_healthy = await handler.health_check()
                else:
                    is_healthy = True  # 假设健康
                
                health_status["handler_status"][name] = is_healthy
                
                if is_healthy:
                    health_status["healthy_handlers"] += 1
                else:
                    health_status["unhealthy_handlers"].append(name)
                    health_status["registry_healthy"] = False
                    
            except Exception as e:
                health_status["handler_status"][name] = False
                health_status["unhealthy_handlers"].append(f"{name}: {e}")
                health_status["registry_healthy"] = False
        
        return health_status


# 全局注册中心实例
_global_registry = None

def get_function_call_registry() -> FunctionCallRegistry:
    """获取全局FunctionCall注册中心"""
    global _global_registry
    if _global_registry is None:
        _global_registry = FunctionCallRegistry()
    return _global_registry